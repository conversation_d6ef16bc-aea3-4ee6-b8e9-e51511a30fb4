# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/infra_ai_analyst"
DB_HOST="localhost"
DB_PORT="5432"
DB_NAME="infra_ai_analyst"
DB_USER="username"
DB_PASSWORD="password"

# Server Configuration
PORT=5000
NODE_ENV="development"
CLIENT_URL="http://localhost:3000"
ADMIN_URL="http://localhost:3000/admin"

# Security
JWT_SECRET="your-super-secret-jwt-key-here"
JWT_EXPIRES_IN="7d"
ADMIN_PASSWORD="your-admin-password-here"
ENCRYPTION_KEY="your-32-character-encryption-key"

# AI APIs
OPENAI_API_KEY="sk-your-openai-api-key"
OPENAI_MODEL="gpt-4"
GOOGLE_AI_API_KEY="your-google-ai-api-key"
GEMINI_MODEL="gemini-pro"

# Google Services
GOOGLE_APPLICATION_CREDENTIALS="path/to/google-service-account.json"
GOOGLE_PROJECT_ID="your-google-project-id"
GOOGLE_CALENDAR_CLIENT_ID="your-google-calendar-client-id"
GOOGLE_CALENDAR_CLIENT_SECRET="your-google-calendar-client-secret"
GOOGLE_TTS_API_KEY="your-google-tts-api-key"

# Payment APIs
STRIPE_SECRET_KEY="sk_test_your-stripe-secret-key"
STRIPE_PUBLISHABLE_KEY="pk_test_your-stripe-publishable-key"
STRIPE_WEBHOOK_SECRET="whsec_your-stripe-webhook-secret"
PAYPAL_CLIENT_ID="your-paypal-client-id"
PAYPAL_CLIENT_SECRET="your-paypal-client-secret"
PAYPAL_MODE="sandbox" # or "live" for production

# Email Configuration
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_SECURE="false"
SMTP_USER="<EMAIL>"
SMTP_PASS="your-app-password"
FROM_EMAIL="<EMAIL>"
FROM_NAME="Infra AI Analyst"

# Notification Emails
ADMIN_EMAIL="<EMAIL>"
NOTIFICATION_EMAIL="<EMAIL>"

# External APIs
CALENDLY_API_KEY="your-calendly-api-key"
CALENDLY_USER_URI="https://api.calendly.com/users/your-user-id"

# File Storage
UPLOAD_DIR="uploads"
MAX_FILE_SIZE="********" # 10MB in bytes
ALLOWED_FILE_TYPES="image/jpeg,image/png,image/gif,application/pdf,text/csv,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"

# Rate Limiting
RATE_LIMIT_WINDOW_MS="900000" # 15 minutes
RATE_LIMIT_MAX_REQUESTS="100"

# Redis (for caching and sessions)
REDIS_URL="redis://localhost:6379"
REDIS_PASSWORD=""

# Logging
LOG_LEVEL="info"
LOG_FILE="logs/app.log"

# SEO Configuration
SITE_NAME="Infra AI Analyst"
SITE_DESCRIPTION="Sistema de Análise de Negócios com IA - Automatize seus processos empresariais"
SITE_URL="https://infra-ai-analyst.com"
SITE_KEYWORDS="inteligência artificial,automação,análise de negócios,n8n,chatbot,consultoria"

# Analytics
GOOGLE_ANALYTICS_ID="G-XXXXXXXXXX"
FACEBOOK_PIXEL_ID="your-facebook-pixel-id"

# Social Media
TWITTER_HANDLE="@infraaianalyst"
LINKEDIN_URL="https://linkedin.com/company/infra-ai-analyst"
FACEBOOK_URL="https://facebook.com/infraaianalyst"

# Webhook URLs
N8N_WEBHOOK_URL="http://localhost:5678/webhook/analysis"
ZAPIER_WEBHOOK_URL="https://hooks.zapier.com/hooks/catch/your-webhook-id"

# Development
DEBUG="true"
VERBOSE_LOGGING="true"
ENABLE_CORS="true"
ENABLE_SWAGGER="true"
