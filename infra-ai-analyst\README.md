# Sistema de Análise de Negócios com IA

## Visão Geral

Plataforma web completa e automatizada que atua como um consultor de negócios de IA. O sistema analisa processos de negócios de clientes globalmente, gera planos de automação personalizados, captura leads e fornece um painel de gestão completo.

## Funcionalidades Principais

### Frontend (Cliente)
- **Agente de Análise 24/7**: Chatbot inteligente que mapeia negócios através de perguntas detalhadas
- **Captura de Leads Obrigatória**: Pop-up modal que captura informações antes de exibir resultados
- **Entrega de Resultados**: Blueprint, Roadmap, Plano de Implementação e áudio explicativo
- **Agendamento Integrado**: Botão para agendar reuniões via Calendly/Google Calendar

### Backend & Admin
- **Painel de Administração**: Dashboard completo com métricas de funil em tempo real
- **Geração de JSON N8N**: IA especializada que cria arquivos de automação baseados em templates
- **Notificações Instantâneas**: Alertas por email quando novos leads são capturados
- **Integrações de Pagamento**: Stripe e PayPal para pagamentos globais
- **Otimização SEO**: Estrutura otimizada com glossário técnico para autoridade

## Estrutura do Projeto

```
infra-ai-analyst/
├── client/                 # Frontend React/Vue
│   ├── src/
│   │   ├── components/     # Componentes reutilizáveis
│   │   ├── pages/         # Páginas da aplicação
│   │   ├── services/      # Serviços de API
│   │   └── utils/         # Utilitários
├── server/                # Backend Node.js/Express
│   ├── src/
│   │   ├── controllers/   # Controladores de rotas
│   │   ├── models/        # Modelos de dados
│   │   ├── services/      # Serviços de negócio
│   │   ├── middleware/    # Middlewares
│   │   └── utils/         # Utilitários
├── admin/                 # Painel administrativo
├── database/              # Scripts e migrações do banco
├── agent-templates-base/  # Templates JSON para N8N
└── docs/                  # Documentação

```

## Tecnologias Utilizadas

- **Frontend**: React/Vue.js, TypeScript, Tailwind CSS
- **Backend**: Node.js, Express, TypeScript
- **Banco de Dados**: PostgreSQL/MySQL
- **IA**: OpenAI GPT, Google Gemini
- **Pagamentos**: Stripe, PayPal
- **Agendamento**: Google Calendar API, Calendly
- **Áudio**: Google Text-to-Speech
- **Automação**: N8N JSON Templates

## Instalação e Configuração

### Pré-requisitos
- Node.js 18+
- PostgreSQL/MySQL
- Chaves de API (OpenAI, Google, Stripe, etc.)

### Instalação
```bash
# Instalar dependências
npm run install:all

# Configurar variáveis de ambiente
cp .env.example .env

# Executar migrações do banco
npm run db:migrate

# Iniciar em modo desenvolvimento
npm run dev
```

## Configuração de APIs

### Variáveis de Ambiente Necessárias
```env
# Database
DATABASE_URL=postgresql://user:password@localhost:5432/infra_ai_analyst

# AI APIs
OPENAI_API_KEY=your_openai_key
GOOGLE_AI_API_KEY=your_google_ai_key

# Payment APIs
STRIPE_SECRET_KEY=your_stripe_secret
PAYPAL_CLIENT_ID=your_paypal_client_id

# Google Services
GOOGLE_CALENDAR_CLIENT_ID=your_google_client_id
GOOGLE_TTS_API_KEY=your_google_tts_key

# Email
SMTP_HOST=your_smtp_host
SMTP_USER=your_smtp_user
SMTP_PASS=your_smtp_password

# Security
JWT_SECRET=your_jwt_secret
ADMIN_PASSWORD=your_admin_password
```

## Fluxo de Funcionamento

1. **Usuário acessa o site** → Chatbot inicia conversa
2. **Mapeamento do negócio** → Perguntas detalhadas sobre processos
3. **Solicitação de análise** → Usuário clica "Gerar Minha Análise"
4. **Captura obrigatória** → Pop-up modal com nome e email
5. **Geração de resultados** → IA cria Blueprint, Roadmap e Plano
6. **Entrega completa** → Resultados + áudio + agendamento
7. **Notificação admin** → Email instantâneo + arquivo JSON N8N
8. **Acompanhamento** → Dashboard admin com métricas e leads

## Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## Licença

Este projeto está licenciado sob a Licença MIT - veja o arquivo [LICENSE](LICENSE) para detalhes.

## Suporte

Para suporte, entre em contato através do email: <EMAIL>

## Roadmap

- [ ] Implementação do chatbot base
- [ ] Sistema de captura de leads
- [ ] Geração de análises com IA
- [ ] Painel administrativo
- [ ] Integrações de pagamento
- [ ] Otimização SEO
- [ ] Testes automatizados
- [ ] Deploy em produção
