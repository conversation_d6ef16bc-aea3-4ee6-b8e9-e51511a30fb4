{"name": "Template: Automação E-commerce Completa", "nodes": [{"parameters": {"httpMethod": "POST", "path": "/order-webhook", "responseMode": "responseNode", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "id": "order-webhook", "name": "Novo Pedido", "webhookId": "order-webhook"}, {"parameters": {"model": {"__rl": true, "value": "gpt-4", "mode": "list"}, "options": {"systemMessage": "Você é um assistente especializado em e-commerce para [BUSINESS_TYPE] no setor de [INDUSTRY]. Analise pedidos e gere recomendações personalizadas de produtos, identifique oportunidades de upsell/cross-sell, e determine o perfil do [CUSTOMER_TERM] para segmentação de marketing."}, "prompt": "Analise este pedido:\n\nCliente: {{ $json.customer_name }}\nEmail: {{ $json.customer_email }}\nProdutos: {{ $json.items }}\nValor Total: {{ $json.total_amount }}\nHistórico de Compras: {{ $json.purchase_history }}\n\nGere:\n1. Recomendações de produtos relacionados\n2. Estratégia de follow-up\n3. Segmento do cliente\n4. Próximas ações de marketing"}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1, "position": [460, 300], "id": "ai-order-analysis", "name": "Análise IA do Pedido", "credentials": {"openAiApi": {"id": "openai-credentials", "name": "OpenAI API"}}}, {"parameters": {"authentication": "oAuth2", "select": "sheet", "sheetId": {"__rl": true, "value": "ECOMMERCE_ORDERS_SHEET_ID", "mode": "list"}, "operation": "append", "columns": {"mappingMode": "defineBelow", "value": {"Order_ID": "={{ $('Novo Pedido').item.json.order_id }}", "Customer_Name": "={{ $('Novo Pedido').item.json.customer_name }}", "Customer_Email": "={{ $('Novo Pedido').item.json.customer_email }}", "Total_Amount": "={{ $('Novo Pedido').item.json.total_amount }}", "Items": "={{ $('Novo Pedido').item.json.items }}", "Customer_Segment": "={{ $('Análise IA do Pedido').item.json.segment }}", "Recommendations": "={{ $('Análise IA do Pedido').item.json.recommendations }}", "Order_Date": "={{ $now }}", "Status": "Processando"}}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4, "position": [680, 200], "id": "save-order-data", "name": "Salvar Dados do Pedido", "credentials": {"googleSheetsOAuth2Api": {"id": "google-sheets-credentials", "name": "Google Sheets"}}}, {"parameters": {"fromEmail": "pedidos@[BUSINESS_DOMAIN]", "toEmail": "={{ $('Novo Pedido').item.json.customer_email }}", "subject": "Confirmação do Pedido #{{ $('Novo Pedido').item.json.order_id }} - [BUSINESS_TYPE]", "emailType": "html", "message": "<!DOCTYPE html>\n<html>\n<head>\n    <meta charset=\"utf-8\">\n    <title>Confirmação de Pedido</title>\n</head>\n<body>\n    <h2>Pedido Confirmado! 🎉</h2>\n    \n    <p>O<PERSON><PERSON> {{ $('Novo Pedido').item.json.customer_name }}!</p>\n    \n    <p>Seu pedido #{{ $('Novo Pedido').item.json.order_id }} foi confirmado com sucesso!</p>\n    \n    <h3>Detalhes do Pedido:</h3>\n    <p><strong>Produtos:</strong> {{ $('Novo Pedido').item.json.items }}</p>\n    <p><strong>Total:</strong> R$ {{ $('Novo Pedido').item.json.total_amount }}</p>\n    \n    <h3>🎯 Recomendações Especiais para Você:</h3>\n    <p>{{ $('Análise IA do Pedido').item.json.recommendations }}</p>\n    \n    <p>Acompanhe seu pedido em: [TRACKING_URL]</p>\n    \n    <p>Obri<PERSON> por escolher a [BUSINESS_TYPE]!</p>\n</body>\n</html>", "options": {}}, "type": "n8n-nodes-base.emailSend", "typeVersion": 2, "position": [680, 400], "id": "order-confirmation", "name": "Confirmação do Pedido", "credentials": {"smtp": {"id": "smtp-credentials", "name": "SMTP"}}}, {"parameters": {"conditions": {"number": [{"value1": "={{ $('Novo Pedido').item.json.total_amount }}", "operation": "largerEqual", "value2": 500}]}}, "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [900, 300], "id": "check-high-value", "name": "Verificar Alto Valor"}, {"parameters": {"fromEmail": "vip@[BUSINESS_DOMAIN]", "toEmail": "={{ $('Novo Pedido').item.json.customer_email }}", "subject": "🌟 Oferta VIP Exclusiva - [BUSINESS_TYPE]", "emailType": "html", "message": "<!DOCTYPE html>\n<html>\n<head>\n    <meta charset=\"utf-8\">\n    <title>Oferta VIP</title>\n</head>\n<body>\n    <h2>🌟 Oferta Especial VIP!</h2>\n    \n    <p>O<PERSON><PERSON> {{ $('Novo Pedido').item.json.customer_name }}!</p>\n    \n    <p>Como você é um [CUSTOMER_TERM] especial, temos uma oferta exclusiva:</p>\n    \n    <div style=\"background: #f0f8ff; padding: 20px; border-radius: 10px; margin: 20px 0;\">\n        <h3>🎁 Desconto de 15% na próxima compra!</h3>\n        <p>Use o código: <strong>VIP15</strong></p>\n        <p>Válido por 30 dias</p>\n    </div>\n    \n    <p>{{ $('Análise IA do Pedido').item.json.upsell_message }}</p>\n    \n    <p>Aproveite esta oportunidade exclusiva!</p>\n</body>\n</html>", "options": {}}, "type": "n8n-nodes-base.emailSend", "typeVersion": 2, "position": [1120, 200], "id": "vip-offer", "name": "Oferta VIP", "credentials": {"smtp": {"id": "smtp-credentials", "name": "SMTP"}}}, {"parameters": {"unit": "days", "amount": 3}, "type": "n8n-nodes-base.wait", "typeVersion": 1, "position": [900, 500], "id": "wait-3-days", "name": "Aguardar 3 Dias"}, {"parameters": {"fromEmail": "suporte@[BUSINESS_DOMAIN]", "toEmail": "={{ $('Novo Pedido').item.json.customer_email }}", "subject": "Como está sua experiência com {{ $('Novo Pedido').item.json.items }}?", "emailType": "html", "message": "<!DOCTYPE html>\n<html>\n<head>\n    <meta charset=\"utf-8\">\n    <title>Feedback</title>\n</head>\n<body>\n    <h2>Como está sua experiência? 😊</h2>\n    \n    <p><PERSON><PERSON><PERSON> {{ $('Novo Pedido').item.json.customer_name }}!</p>\n    \n    <p>Esperamos que esteja aproveitando seu(s) {{ $('Novo Pedido').item.json.items }}!</p>\n    \n    <p>Sua opinião é muito importante para nós. Que tal nos contar como foi sua experiência?</p>\n    \n    <div style=\"text-align: center; margin: 30px 0;\">\n        <a href=\"[REVIEW_URL]\" style=\"background: #4CAF50; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px;\">Deixar Avaliação</a>\n    </div>\n    \n    <p>Como agradecimento, você ganhará 10% de desconto na próxima compra!</p>\n    \n    <p><PERSON><PERSON><PERSON> por escolher a [BUSINESS_TYPE]!</p>\n</body>\n</html>", "options": {}}, "type": "n8n-nodes-base.emailSend", "typeVersion": 2, "position": [1120, 500], "id": "feedback-request", "name": "<PERSON><PERSON><PERSON>", "credentials": {"smtp": {"id": "smtp-credentials", "name": "SMTP"}}}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"success\": true,\n  \"message\": \"Pedido processado com sucesso!\",\n  \"orderId\": \"{{ $('Novo Pedido').item.json.order_id }}\",\n  \"customerSegment\": \"{{ $('Análise IA do Pedido').item.json.segment }}\",\n  \"automationTriggered\": true\n}", "options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 600], "id": "webhook-response", "name": "Resposta Webhook"}], "connections": {"Novo Pedido": {"main": [[{"node": "Análise IA do Pedido", "type": "main", "index": 0}]]}, "Análise IA do Pedido": {"main": [[{"node": "Salvar Dados do Pedido", "type": "main", "index": 0}, {"node": "Confirmação do Pedido", "type": "main", "index": 0}, {"node": "Verificar Alto Valor", "type": "main", "index": 0}]]}, "Confirmação do Pedido": {"main": [[{"node": "Aguardar 3 Dias", "type": "main", "index": 0}, {"node": "Resposta Webhook", "type": "main", "index": 0}]]}, "Verificar Alto Valor": {"main": [[{"node": "Oferta VIP", "type": "main", "index": 0}], []]}, "Aguardar 3 Dias": {"main": [[{"node": "<PERSON><PERSON><PERSON>", "type": "main", "index": 0}]]}}, "active": false, "settings": {}, "versionId": "1", "meta": {"templateCreatedBy": "Infra AI Analyst", "description": "Template completo para automação de e-commerce com análise de pedidos, segmentação de clientes e follow-up automatizado", "categories": ["E-commerce", "Customer Segmentation", "Marketing Automation", "AI"], "version": "1.0"}}