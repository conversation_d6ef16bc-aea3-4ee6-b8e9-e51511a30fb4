{"name": "Template: Gestão de Leads Automatizada", "nodes": [{"parameters": {"httpMethod": "POST", "path": "/lead-capture", "responseMode": "responseNode", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "id": "webhook-lead-capture", "name": "Captura de Lead", "webhookId": "lead-capture-webhook"}, {"parameters": {"model": {"__rl": true, "value": "gpt-4", "mode": "list"}, "options": {"systemMessage": "Você é um assistente especializado em qualificação de leads para [BUSINESS_TYPE] no setor de [INDUSTRY]. <PERSON><PERSON><PERSON> as informações do lead e determine o nível de qualificação (Quente, Morno, Frio) baseado nos critérios: interesse demonstrado, fit com o perfil ideal, urgência da necessidade e orçamento disponível. Forneça uma pontuação de 1-10 e justificativa."}, "prompt": "Analise este lead:\n\nNome: {{ $json.name }}\nEmail: {{ $json.email }}\nEmpresa: {{ $json.company }}\nCargo: {{ $json.position }}\nInteresse: {{ $json.interest }}\nOrçamento: {{ $json.budget }}\nUrgência: {{ $json.urgency }}\n\nClassifique este lead e forneça recomendações de próximos passos."}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1, "position": [460, 300], "id": "ai-lead-qualification", "name": "Qualificação IA", "credentials": {"openAiApi": {"id": "openai-credentials", "name": "OpenAI API"}}}, {"parameters": {"conditions": {"string": [{"value1": "={{ $json.score }}", "operation": "largerEqual", "value2": "8"}]}}, "type": "n8n-nodes-base.if", "typeVersion": 1, "position": [680, 300], "id": "lead-score-check", "name": "Verificar Pontuação"}, {"parameters": {"authentication": "oAuth2", "select": "sheet", "sheetId": {"__rl": true, "value": "BUSINESS_LEADS_SHEET_ID", "mode": "list"}, "operation": "append", "columns": {"mappingMode": "defineBelow", "value": {"Nome": "={{ $('Captura de Lead').item.json.name }}", "Email": "={{ $('Captura de <PERSON>').item.json.email }}", "Empresa": "={{ $('Captura de Lead').item.json.company }}", "Cargo": "={{ $('Captura de Lead').item.json.position }}", "Pontuacao": "={{ $('Qualificação IA').item.json.score }}", "Classificacao": "={{ $('Qualificação IA').item.json.classification }}", "Data_Captura": "={{ $now }}", "Status": "Novo"}, "matchingColumns": [], "schema": []}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4, "position": [900, 200], "id": "save-to-sheet", "name": "<PERSON>var no Sheets", "credentials": {"googleSheetsOAuth2Api": {"id": "google-sheets-credentials", "name": "Google Sheets"}}}, {"parameters": {"fromEmail": "noreply@[BUSINESS_DOMAIN]", "toEmail": "={{ $('Captura de <PERSON>').item.json.email }}", "subject": "Bem-vindo à [BUSINESS_TYPE] - Próximos <PERSON>", "emailType": "html", "message": "<!DOCTYPE html>\n<html>\n<head>\n    <meta charset=\"utf-8\">\n    <title>Bem-vindo</title>\n</head>\n<body>\n    <h2><PERSON><PERSON><PERSON> {{ $('Captura de Lead').item.json.name }}!</h2>\n    \n    <p>Obrigado pelo seu interesse em nossa [OFFERING_TERM]!</p>\n    \n    <p>Com base nas informações fornecidas, nossa equipe irá entrar em contato em breve para apresentar uma solução personalizada para {{ $('Captura de Lead').item.json.company }}.</p>\n    \n    <p><strong>Próximos passos:</strong></p>\n    <ul>\n        <li>Análise detalhada das suas necessidades</li>\n        <li>Proposta personalizada</li>\n        <li>Demonstração da solução</li>\n    </ul>\n    \n    <p>Atenciosamente,<br>\n    Equipe [BUSINESS_TYPE]<br>\n    Especialistas em [INDUSTRY]</p>\n</body>\n</html>", "options": {}}, "type": "n8n-nodes-base.emailSend", "typeVersion": 2, "position": [900, 400], "id": "welcome-email", "name": "<PERSON><PERSON>", "credentials": {"smtp": {"id": "smtp-credentials", "name": "SMTP"}}}, {"parameters": {"fromEmail": "vendas@[BUSINESS_DOMAIN]", "toEmail": "vendas@[BUSINESS_DOMAIN]", "subject": "🔥 Lead Quente Capturado: {{ $('Captura de Lead').item.json.name }}", "emailType": "html", "message": "<!DOCTYPE html>\n<html>\n<head>\n    <meta charset=\"utf-8\">\n    <title>Novo Lead Quente</title>\n</head>\n<body>\n    <h2>🔥 Novo Lead Quente Capturado!</h2>\n    \n    <p><strong>Informações do Lead:</strong></p>\n    <ul>\n        <li><strong>Nome:</strong> {{ $('Captura de Lead').item.json.name }}</li>\n        <li><strong>Email:</strong> {{ $('Captura de Lead').item.json.email }}</li>\n        <li><strong>Empresa:</strong> {{ $('Captura de Lead').item.json.company }}</li>\n        <li><strong>Cargo:</strong> {{ $('Captura de Lead').item.json.position }}</li>\n        <li><strong>Pontuação IA:</strong> {{ $('Qualificação IA').item.json.score }}/10</li>\n    </ul>\n    \n    <p><strong><PERSON><PERSON><PERSON><PERSON> da IA:</strong></p>\n    <p>{{ $('Qualificação IA').item.json.analysis }}</p>\n    \n    <p><strong>Recomendações:</strong></p>\n    <p>{{ $('Qualificação IA').item.json.recommendations }}</p>\n    \n    <p><em>Este lead foi automaticamente qualificado como QUENTE. Entre em contato o mais rápido possível!</em></p>\n</body>\n</html>", "options": {}}, "type": "n8n-nodes-base.emailSend", "typeVersion": 2, "position": [1120, 200], "id": "hot-lead-alert", "name": "Alerta Lead Quente", "credentials": {"smtp": {"id": "smtp-credentials", "name": "SMTP"}}}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"success\": true,\n  \"message\": \"Lead capturado com sucesso!\",\n  \"leadId\": \"{{ $('Captura de Lead').item.json.id }}\",\n  \"score\": {{ $('Qualificação IA').item.json.score }},\n  \"classification\": \"{{ $('Qualificação IA').item.json.classification }}\"\n}", "options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [900, 600], "id": "webhook-response", "name": "Resposta Webhook"}], "connections": {"Captura de Lead": {"main": [[{"node": "Qualificação IA", "type": "main", "index": 0}]]}, "Qualificação IA": {"main": [[{"node": "Verificar Pontuação", "type": "main", "index": 0}]]}, "Verificar Pontuação": {"main": [[{"node": "<PERSON>var no Sheets", "type": "main", "index": 0}, {"node": "Alerta Lead Quente", "type": "main", "index": 0}], [{"node": "<PERSON>var no Sheets", "type": "main", "index": 0}]]}, "Salvar no Sheets": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Email de Boas-vindas": {"main": [[{"node": "Resposta Webhook", "type": "main", "index": 0}]]}, "Alerta Lead Quente": {"main": [[{"node": "Resposta Webhook", "type": "main", "index": 0}]]}}, "active": false, "settings": {}, "versionId": "1", "meta": {"templateCreatedBy": "Infra AI Analyst", "description": "Template para automação de gestão de leads com qualificação por IA", "categories": ["Lead Management", "Sales Automation", "AI"], "version": "1.0"}}