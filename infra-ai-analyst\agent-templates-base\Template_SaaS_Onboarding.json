{"name": "Template: Onboarding SaaS Automatizado", "nodes": [{"parameters": {"httpMethod": "POST", "path": "/user-signup", "responseMode": "responseNode", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 1, "position": [240, 300], "id": "user-signup", "name": "Novo Usuário", "webhookId": "user-signup-webhook"}, {"parameters": {"model": {"__rl": true, "value": "gpt-4", "mode": "list"}, "options": {"systemMessage": "Você é um especialista em onboarding para [BUSINESS_TYPE] SaaS no setor de [INDUSTRY]. Analise o perfil do novo [CUSTOMER_TERM] e crie um plano de onboarding personalizado baseado no cargo, empresa, caso de uso e nível de experiência técnica."}, "prompt": "Novo usuário cadastrado:\n\nNome: {{ $json.name }}\nEmail: {{ $json.email }}\nEmpresa: {{ $json.company }}\nCargo: {{ $json.position }}\n<PERSON><PERSON><PERSON> da Empresa: {{ $json.company_size }}\n<PERSON><PERSON>o de Uso: {{ $json.use_case }}\nExperiência Técnica: {{ $json.tech_level }}\nPlano: {{ $json.plan }}\n\nCrie um plano de onboarding personalizado com:\n1. Sequência de emails educativos\n2. Recursos recomendados\n3. <PERSON> de sucesso\n4. Cronograma sugerido"}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1, "position": [460, 300], "id": "ai-onboarding-plan", "name": "Plano de Onboarding IA", "credentials": {"openAiApi": {"id": "openai-credentials", "name": "OpenAI API"}}}, {"parameters": {"authentication": "oAuth2", "select": "sheet", "sheetId": {"__rl": true, "value": "SAAS_USERS_SHEET_ID", "mode": "list"}, "operation": "append", "columns": {"mappingMode": "defineBelow", "value": {"User_ID": "={{ $('Novo Usuário').item.json.user_id }}", "Name": "={{ $('Novo Usuário').item.json.name }}", "Email": "={{ $('Novo Usuário').item.json.email }}", "Company": "={{ $('Novo Usuário').item.json.company }}", "Position": "={{ $('Novo Usuário').item.json.position }}", "Plan": "={{ $('Novo Usuário').item.json.plan }}", "Use_Case": "={{ $('Novo Usuário').item.json.use_case }}", "Tech_Level": "={{ $('Novo Usuário').item.json.tech_level }}", "Onboarding_Plan": "={{ $('Plano de Onboarding IA').item.json.plan }}", "Signup_Date": "={{ $now }}", "Status": "Onboarding", "Trial_End": "={{ $now.plus({days: 14}) }}"}}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4, "position": [680, 200], "id": "save-user-data", "name": "Salvar Dados do Usuário", "credentials": {"googleSheetsOAuth2Api": {"id": "google-sheets-credentials", "name": "Google Sheets"}}}, {"parameters": {"fromEmail": "onboarding@[BUSINESS_DOMAIN]", "toEmail": "={{ $('Novo Usuário').item.json.email }}", "subject": "🚀 Bem-vindo à [BUSINESS_TYPE]! Vamos começar?", "emailType": "html", "message": "<!DOCTYPE html>\n<html>\n<head>\n    <meta charset=\"utf-8\">\n    <title>Bem-vindo</title>\n</head>\n<body>\n    <h2>🚀 Bem-vindo à [BUSINESS_TYPE], {{ $('Novo Usuário').item.json.name }}!</h2>\n    \n    <p>Estamos muito felizes em tê-lo conosco!</p>\n    \n    <p>Criamos um plano personalizado para você aproveitar ao máximo nossa [OFFERING_TERM]:</p>\n    \n    <div style=\"background: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;\">\n        <h3>🎯 Seu Plano de Onboarding Personalizado:</h3>\n        <p>{{ $('Plano de Onboarding IA').item.json.plan }}</p>\n    </div>\n    \n    <h3>📚 Primeiros Passos:</h3>\n    <ol>\n        <li><a href=\"[SETUP_URL]\">Configure sua conta</a></li>\n        <li><a href=\"[TUTORIAL_URL]\">Assista ao tutorial de 5 minutos</a></li>\n        <li><a href=\"[DEMO_DATA_URL]\">Importe dados de exemplo</a></li>\n    </ol>\n    \n    <p><strong>💡 Dica:</strong> {{ $('Plano de Onboarding IA').item.json.first_tip }}</p>\n    \n    <p>Precisa de ajuda? Responda este email ou agende uma call: [CALENDAR_URL]</p>\n    \n    <p>Vamos juntos transformar {{ $('Novo Usuário').item.json.company }}!</p>\n</body>\n</html>", "options": {}}, "type": "n8n-nodes-base.emailSend", "typeVersion": 2, "position": [680, 400], "id": "welcome-email", "name": "<PERSON><PERSON>", "credentials": {"smtp": {"id": "smtp-credentials", "name": "SMTP"}}}, {"parameters": {"unit": "days", "amount": 1}, "type": "n8n-nodes-base.wait", "typeVersion": 1, "position": [900, 300], "id": "wait-1-day", "name": "Aguardar 1 Dia"}, {"parameters": {"fromEmail": "success@[BUSINESS_DOMAIN]", "toEmail": "={{ $('Novo Usuário').item.json.email }}", "subject": "📈 Como está indo seu primeiro dia na [BUSINESS_TYPE]?", "emailType": "html", "message": "<!DOCTYPE html>\n<html>\n<head>\n    <meta charset=\"utf-8\">\n    <title>Check-in Dia 1</title>\n</head>\n<body>\n    <h2>📈 Como está indo, {{ $('Novo Usuário').item.json.name }}?</h2>\n    \n    <p>Esperamos que esteja explorando nossa [OFFERING_TERM]!</p>\n    \n    <h3>✅ Checklist do Primeiro Dia:</h3>\n    <ul>\n        <li>□ Configurou sua conta</li>\n        <li>□ Assistiu ao tutorial</li>\n        <li>□ Importou dados de exemplo</li>\n        <li>□ Criou seu primeiro projeto</li>\n    </ul>\n    \n    <p><strong>🎯 Próximo Marco:</strong> {{ $('Plano de Onboarding IA').item.json.day2_goal }}</p>\n    \n    <div style=\"background: #e3f2fd; padding: 15px; border-radius: 8px; margin: 20px 0;\">\n        <h4>💡 Dica Especial para {{ $('Novo Usuário').item.json.use_case }}:</h4>\n        <p>{{ $('Plano de Onboarding IA').item.json.use_case_tip }}</p>\n    </div>\n    \n    <p>Precisa de ajuda? Nossa equipe está aqui para você!</p>\n    \n    <p><a href=\"[HELP_URL]\" style=\"background: #2196F3; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;\">Falar com Especialista</a></p>\n</body>\n</html>", "options": {}}, "type": "n8n-nodes-base.emailSend", "typeVersion": 2, "position": [1120, 300], "id": "day1-checkin", "name": "Check-in Dia 1", "credentials": {"smtp": {"id": "smtp-credentials", "name": "SMTP"}}}, {"parameters": {"unit": "days", "amount": 6}, "type": "n8n-nodes-base.wait", "typeVersion": 1, "position": [1340, 300], "id": "wait-6-days", "name": "Aguardar 6 Dias"}, {"parameters": {"fromEmail": "growth@[BUSINESS_DOMAIN]", "toEmail": "={{ $('Novo Usuário').item.json.email }}", "subject": "🎯 Semana 1 completa! Vamos ao próximo nível?", "emailType": "html", "message": "<!DOCTYPE html>\n<html>\n<head>\n    <meta charset=\"utf-8\">\n    <title>Semana 1 Completa</title>\n</head>\n<body>\n    <h2>🎯 Parabéns, {{ $('Novo Usuário').item.json.name }}!</h2>\n    \n    <p>Você completou sua primeira semana na [BUSINESS_TYPE]! 🎉</p>\n    \n    <h3>📊 Recursos Avançados para {{ $('Novo Usuário').item.json.position }}:</h3>\n    <p>{{ $('Plano de Onboarding IA').item.json.advanced_features }}</p>\n    \n    <h3>🚀 Próximos Passos:</h3>\n    <ol>\n        <li>{{ $('Plano de Onboarding IA').item.json.week2_step1 }}</li>\n        <li>{{ $('Plano de Onboarding IA').item.json.week2_step2 }}</li>\n        <li>{{ $('Plano de Onboarding IA').item.json.week2_step3 }}</li>\n    </ol>\n    \n    <div style=\"background: #fff3e0; padding: 20px; border-radius: 10px; margin: 20px 0;\">\n        <h4>💼 Caso de Sucesso Similar:</h4>\n        <p>Empresas como {{ $('Novo Usuário').item.json.company }} conseguiram {{ $('Plano de Onboarding IA').item.json.success_story }} usando nossa [OFFERING_TERM].</p>\n    </div>\n    \n    <p>Quer acelerar seus resultados? Agende uma sessão de estratégia gratuita:</p>\n    \n    <p><a href=\"[STRATEGY_CALL_URL]\" style=\"background: #FF9800; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px;\">Agendar Sessão de Estratégia</a></p>\n</body>\n</html>", "options": {}}, "type": "n8n-nodes-base.emailSend", "typeVersion": 2, "position": [1560, 300], "id": "week1-complete", "name": "Semana 1 Completa", "credentials": {"smtp": {"id": "smtp-credentials", "name": "SMTP"}}}, {"parameters": {"respondWith": "json", "responseBody": "{\n  \"success\": true,\n  \"message\": \"Usuário registrado e onboarding iniciado!\",\n  \"userId\": \"{{ $('Novo Usuário').item.json.user_id }}\",\n  \"onboardingPlan\": \"{{ $('Plano de Onboarding IA').item.json.plan_summary }}\",\n  \"trialEnd\": \"{{ $now.plus({days: 14}) }}\"\n}", "options": {}}, "type": "n8n-nodes-base.respondToWebhook", "typeVersion": 1, "position": [680, 600], "id": "webhook-response", "name": "Resposta Webhook"}], "connections": {"Novo Usuário": {"main": [[{"node": "Plano de Onboarding IA", "type": "main", "index": 0}]]}, "Plano de Onboarding IA": {"main": [[{"node": "Salvar Dados do Usuário", "type": "main", "index": 0}, {"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Email de Boas-vindas": {"main": [[{"node": "Aguardar 1 Dia", "type": "main", "index": 0}, {"node": "Resposta Webhook", "type": "main", "index": 0}]]}, "Aguardar 1 Dia": {"main": [[{"node": "Check-in Dia 1", "type": "main", "index": 0}]]}, "Check-in Dia 1": {"main": [[{"node": "Aguardar 6 Dias", "type": "main", "index": 0}]]}, "Aguardar 6 Dias": {"main": [[{"node": "Semana 1 Completa", "type": "main", "index": 0}]]}}, "active": false, "settings": {}, "versionId": "1", "meta": {"templateCreatedBy": "Infra AI Analyst", "description": "Template para onboarding automatizado de usuários SaaS com personalização baseada em IA", "categories": ["SaaS", "Onboarding", "User Engagement", "AI"], "version": "1.0"}}