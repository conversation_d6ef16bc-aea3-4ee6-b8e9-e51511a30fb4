{"name": "infra-ai-analyst-client", "version": "1.0.0", "description": "Frontend do Sistema de Análise de Negócios com IA", "private": true, "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.18.0", "axios": "^1.6.0", "socket.io-client": "^4.7.4", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "framer-motion": "^10.16.5", "react-hook-form": "^7.47.0", "react-hot-toast": "^2.4.1", "react-query": "^3.39.3", "zustand": "^4.4.6", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0", "lucide-react": "^0.292.0", "react-markdown": "^9.0.1", "react-syntax-highlighter": "^15.5.0", "react-confetti": "^6.1.0", "react-audio-player": "^0.17.0", "react-calendar": "^4.6.0", "stripe": "^14.5.0", "@stripe/stripe-js": "^2.1.11", "@stripe/react-stripe-js": "^2.4.0", "moment": "^2.29.4", "uuid": "^9.0.1"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/uuid": "^9.0.7", "@types/react-syntax-highlighter": "^15.5.10", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "vite": "^4.5.0", "vitest": "^0.34.6", "@vitest/ui": "^0.34.6"}, "engines": {"node": ">=18.0.0"}}