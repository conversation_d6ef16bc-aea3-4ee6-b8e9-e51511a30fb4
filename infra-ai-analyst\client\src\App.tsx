import React, { Suspense, lazy } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import { ErrorBoundary } from 'react-error-boundary';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';

// Components
import { LoadingSpinner } from './components/UI/LoadingSpinner';
import { ErrorFallback } from './components/UI/ErrorFallback';
import { Layout } from './components/Layout/Layout';

// Lazy load pages
const HomePage = lazy(() => import('./pages/HomePage'));
const ChatPage = lazy(() => import('./pages/ChatPage'));
const AnalysisPage = lazy(() => import('./pages/AnalysisPage'));
const AdminPage = lazy(() => import('./pages/AdminPage'));
const AdminLoginPage = lazy(() => import('./pages/AdminLoginPage'));
const NotFoundPage = lazy(() => import('./pages/NotFoundPage'));

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
    mutations: {
      retry: 1,
    },
  },
});

// Loading component
const PageLoader: React.FC = () => (
  <div className="min-h-screen flex items-center justify-center">
    <LoadingSpinner size="lg" />
  </div>
);

// Error handler
const handleError = (error: Error, errorInfo: { componentStack: string }) => {
  console.error('Application error:', error, errorInfo);
  
  // You can send error to monitoring service here
  if (process.env.NODE_ENV === 'production') {
    // Example: Sentry.captureException(error);
  }
};

function App() {
  return (
    <ErrorBoundary FallbackComponent={ErrorFallback} onError={handleError}>
      <QueryClientProvider client={queryClient}>
        <Router>
          <div className="App">
            <Routes>
              {/* Public Routes */}
              <Route
                path="/"
                element={
                  <Layout>
                    <Suspense fallback={<PageLoader />}>
                      <HomePage />
                    </Suspense>
                  </Layout>
                }
              />
              
              <Route
                path="/chat"
                element={
                  <Layout>
                    <Suspense fallback={<PageLoader />}>
                      <ChatPage />
                    </Suspense>
                  </Layout>
                }
              />
              
              <Route
                path="/analysis/:analysisId"
                element={
                  <Layout>
                    <Suspense fallback={<PageLoader />}>
                      <AnalysisPage />
                    </Suspense>
                  </Layout>
                }
              />

              {/* Admin Routes */}
              <Route
                path="/admin/login"
                element={
                  <Suspense fallback={<PageLoader />}>
                    <AdminLoginPage />
                  </Suspense>
                }
              />
              
              <Route
                path="/admin"
                element={
                  <Suspense fallback={<PageLoader />}>
                    <AdminPage />
                  </Suspense>
                }
              />

              {/* Redirects */}
              <Route path="/dashboard" element={<Navigate to="/admin" replace />} />
              <Route path="/login" element={<Navigate to="/admin/login" replace />} />

              {/* 404 */}
              <Route
                path="*"
                element={
                  <Layout>
                    <Suspense fallback={<PageLoader />}>
                      <NotFoundPage />
                    </Suspense>
                  </Layout>
                }
              />
            </Routes>

            {/* Global Toast Notifications */}
            <Toaster
              position="top-right"
              toastOptions={{
                duration: 4000,
                style: {
                  background: '#363636',
                  color: '#fff',
                  borderRadius: '8px',
                  padding: '16px',
                  fontSize: '14px',
                  maxWidth: '500px',
                },
                success: {
                  iconTheme: {
                    primary: '#4ade80',
                    secondary: '#fff',
                  },
                },
                error: {
                  iconTheme: {
                    primary: '#ef4444',
                    secondary: '#fff',
                  },
                  duration: 6000,
                },
                loading: {
                  iconTheme: {
                    primary: '#3b82f6',
                    secondary: '#fff',
                  },
                },
              }}
            />

            {/* React Query Devtools (only in development) */}
            {process.env.NODE_ENV === 'development' && (
              <ReactQueryDevtools initialIsOpen={false} />
            )}
          </div>
        </Router>
      </QueryClientProvider>
    </ErrorBoundary>
  );
}

export default App;
