import React from 'react';
import { motion } from 'framer-motion';
import { CheckCircleIcon, ClockIcon } from '@heroicons/react/24/outline';

interface AnalysisReadinessProps {
  score: number;
  isReady: boolean;
  className?: string;
}

export const AnalysisReadiness: React.FC<AnalysisReadinessProps> = ({ 
  score, 
  isReady, 
  className = '' 
}) => {
  const getStatusColor = () => {
    if (isReady) return 'text-green-400';
    if (score >= 70) return 'text-yellow-400';
    if (score >= 40) return 'text-orange-400';
    return 'text-gray-400';
  };

  const getStatusText = () => {
    if (isReady) return 'Pronto para análise!';
    if (score >= 70) return 'Quase pronto...';
    if (score >= 40) return 'Coletando dados...';
    return 'Iniciando conversa...';
  };

  const getProgressColor = () => {
    if (isReady) return 'bg-green-400';
    if (score >= 70) return 'bg-yellow-400';
    if (score >= 40) return 'bg-orange-400';
    return 'bg-gray-400';
  };

  return (
    <div className={`${className}`}>
      <div className="flex items-center justify-between text-sm">
        <div className="flex items-center space-x-2">
          {isReady ? (
            <CheckCircleIcon className="w-4 h-4 text-green-400" />
          ) : (
            <ClockIcon className="w-4 h-4 text-white/70" />
          )}
          <span className={`font-medium ${getStatusColor()}`}>
            {getStatusText()}
          </span>
        </div>
        <span className="text-white/70 text-xs">
          {Math.round(score)}%
        </span>
      </div>
      
      {/* Progress Bar */}
      <div className="mt-2 w-full bg-white/20 rounded-full h-2 overflow-hidden">
        <motion.div
          className={`h-full ${getProgressColor()} rounded-full`}
          initial={{ width: 0 }}
          animate={{ width: `${score}%` }}
          transition={{ duration: 0.5, ease: "easeOut" }}
        />
      </div>
      
      {/* Ready Indicator */}
      {isReady && (
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          className="mt-2 text-xs text-green-400 font-medium"
        >
          ✨ Clique em "Gerar Análise" quando estiver pronto!
        </motion.div>
      )}
    </div>
  );
};
