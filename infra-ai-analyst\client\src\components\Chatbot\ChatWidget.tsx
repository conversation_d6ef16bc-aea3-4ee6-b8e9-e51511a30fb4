import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  ChatBubbleLeftRightIcon, 
  XMarkIcon, 
  PaperAirplaneIcon,
  SparklesIcon 
} from '@heroicons/react/24/outline';
import { useChatStore } from '../../store/chatStore';
import { ChatMessage } from './ChatMessage';
import { AnalysisReadiness } from './AnalysisReadiness';
import { LoadingDots } from '../UI/LoadingDots';

interface ChatWidgetProps {
  onAnalysisReady?: (sessionId: string) => void;
}

export const ChatWidget: React.FC<ChatWidgetProps> = ({ onAnalysisReady }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [message, setMessage] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  
  const {
    sessionId,
    messages,
    isLoading,
    readinessScore,
    isReady,
    startSession,
    sendMessage: sendChatMessage,
    error
  } = useChatStore();

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Start chat session when widget opens
  useEffect(() => {
    if (isOpen && !sessionId) {
      startSession();
    }
  }, [isOpen, sessionId, startSession]);

  // Handle analysis readiness
  useEffect(() => {
    if (isReady && sessionId && onAnalysisReady) {
      onAnalysisReady(sessionId);
    }
  }, [isReady, sessionId, onAnalysisReady]);

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!message.trim() || isLoading || !sessionId) return;

    const userMessage = message.trim();
    setMessage('');
    setIsTyping(true);

    try {
      await sendChatMessage(userMessage, sessionId);
    } catch (error) {
      console.error('Error sending message:', error);
    } finally {
      setIsTyping(false);
    }
  };

  const toggleChat = () => {
    setIsOpen(!isOpen);
  };

  return (
    <>
      {/* Chat Widget Button */}
      <motion.button
        onClick={toggleChat}
        className={`fixed bottom-6 right-6 z-50 p-4 rounded-full shadow-lg transition-all duration-300 ${
          isOpen 
            ? 'bg-red-500 hover:bg-red-600' 
            : 'bg-primary-600 hover:bg-primary-700'
        } text-white`}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ type: "spring", stiffness: 260, damping: 20 }}
      >
        {isOpen ? (
          <XMarkIcon className="w-6 h-6" />
        ) : (
          <div className="relative">
            <ChatBubbleLeftRightIcon className="w-6 h-6" />
            {isReady && (
              <motion.div
                className="absolute -top-1 -right-1 w-3 h-3 bg-green-400 rounded-full"
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ repeat: Infinity, duration: 2 }}
              />
            )}
          </div>
        )}
      </motion.button>

      {/* Chat Window */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: 100, scale: 0.8 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: 100, scale: 0.8 }}
            transition={{ type: "spring", stiffness: 300, damping: 30 }}
            className="fixed bottom-24 right-6 z-40 w-96 h-[600px] bg-white rounded-2xl shadow-2xl border border-gray-200 flex flex-col overflow-hidden"
          >
            {/* Header */}
            <div className="bg-gradient-to-r from-primary-600 to-primary-700 p-4 text-white">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <SparklesIcon className="w-5 h-5" />
                  <h3 className="font-semibold">Consultor de IA</h3>
                </div>
                <button
                  onClick={toggleChat}
                  className="p-1 hover:bg-white/20 rounded-full transition-colors"
                >
                  <XMarkIcon className="w-4 h-4" />
                </button>
              </div>
              
              {/* Analysis Readiness Indicator */}
              {sessionId && (
                <AnalysisReadiness 
                  score={readinessScore} 
                  isReady={isReady}
                  className="mt-2"
                />
              )}
            </div>

            {/* Messages Area */}
            <div className="flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50">
              {messages.map((msg, index) => (
                <ChatMessage
                  key={index}
                  message={msg}
                  isLast={index === messages.length - 1}
                />
              ))}
              
              {/* Typing Indicator */}
              {(isTyping || isLoading) && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="flex items-center space-x-2 text-gray-500"
                >
                  <div className="w-8 h-8 bg-primary-100 rounded-full flex items-center justify-center">
                    <SparklesIcon className="w-4 h-4 text-primary-600" />
                  </div>
                  <div className="bg-white rounded-2xl px-4 py-2 shadow-sm">
                    <LoadingDots />
                  </div>
                </motion.div>
              )}
              
              {/* Error Message */}
              {error && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="bg-red-50 border border-red-200 rounded-lg p-3 text-red-700 text-sm"
                >
                  {error}
                </motion.div>
              )}
              
              <div ref={messagesEndRef} />
            </div>

            {/* Input Area */}
            <form onSubmit={handleSendMessage} className="p-4 border-t border-gray-200 bg-white">
              <div className="flex items-center space-x-2">
                <input
                  type="text"
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  placeholder="Digite sua mensagem..."
                  className="flex-1 px-4 py-2 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  disabled={isLoading || !sessionId}
                  maxLength={2000}
                />
                <button
                  type="submit"
                  disabled={!message.trim() || isLoading || !sessionId}
                  className="p-2 bg-primary-600 text-white rounded-full hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  <PaperAirplaneIcon className="w-5 h-5" />
                </button>
              </div>
              
              {/* Character Counter */}
              <div className="text-xs text-gray-500 mt-1 text-right">
                {message.length}/2000
              </div>
            </form>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};
