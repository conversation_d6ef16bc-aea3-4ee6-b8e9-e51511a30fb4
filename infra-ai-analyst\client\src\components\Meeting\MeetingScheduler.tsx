import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Dialog } from '@headlessui/react';
import { 
  XMarkIcon, 
  CalendarDaysIcon,
  ClockIcon,
  UserIcon,
  EnvelopeIcon,
  ChatBubbleLeftRightIcon
} from '@heroicons/react/24/outline';
import { useForm } from 'react-hook-form';
import { toast } from 'react-hot-toast';
import { meetingApi } from '../../services/meetingApi';

interface MeetingSchedulerProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (meetingId: string) => void;
  analysisId: string;
  businessType: string;
  industry: string;
  defaultClientName?: string;
  defaultClientEmail?: string;
}

interface MeetingFormData {
  clientName: string;
  clientEmail: string;
  preferredDate: string;
  preferredTime: string;
  meetingType: 'consultation' | 'implementation' | 'follow-up';
  notes?: string;
}

export const MeetingScheduler: React.FC<MeetingSchedulerProps> = ({
  isOpen,
  onClose,
  onSuccess,
  analysisId,
  businessType,
  industry,
  defaultClientName = '',
  defaultClientEmail = ''
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedType, setSelectedType] = useState<'consultation' | 'implementation' | 'follow-up'>('consultation');

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    reset,
    setValue,
    watch
  } = useForm<MeetingFormData>({
    mode: 'onChange',
    defaultValues: {
      clientName: defaultClientName,
      clientEmail: defaultClientEmail,
      meetingType: 'consultation'
    }
  });

  const watchedMeetingType = watch('meetingType');

  // Handle form submission
  const onSubmit = async (data: MeetingFormData) => {
    if (!analysisId) {
      toast.error('ID da análise não encontrado. Recarregue a página e tente novamente.');
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await meetingApi.scheduleMeeting({
        ...data,
        analysisId,
        timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
      });

      toast.success('Reunião agendada com sucesso! 🎉');
      
      // Call success callback
      onSuccess(response.data.meetingId);
      
      // Reset form and close modal
      reset();
      onClose();

    } catch (error: any) {
      console.error('Error scheduling meeting:', error);
      toast.error(
        error.response?.data?.error || 
        'Erro ao agendar reunião. Tente novamente.'
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  const meetingTypes = [
    {
      id: 'consultation' as const,
      name: 'Consultoria Inicial',
      duration: '60 minutos',
      description: 'Apresentação da análise e discussão de oportunidades',
      icon: ChatBubbleLeftRightIcon,
      color: 'bg-blue-100 text-blue-600'
    },
    {
      id: 'implementation' as const,
      name: 'Implementação',
      duration: '90 minutos',
      description: 'Planejamento técnico e início da implementação',
      icon: ClockIcon,
      color: 'bg-green-100 text-green-600'
    },
    {
      id: 'follow-up' as const,
      name: 'Follow-up',
      duration: '30 minutos',
      description: 'Acompanhamento de progresso e ajustes',
      icon: CalendarDaysIcon,
      color: 'bg-purple-100 text-purple-600'
    }
  ];

  // Get minimum date (tomorrow)
  const getMinDate = () => {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    return tomorrow.toISOString().split('T')[0];
  };

  // Get maximum date (30 days from now)
  const getMaxDate = () => {
    const maxDate = new Date();
    maxDate.setDate(maxDate.getDate() + 30);
    return maxDate.toISOString().split('T')[0];
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <Dialog
          as={motion.div}
          static
          open={isOpen}
          onClose={handleClose}
          className="fixed inset-0 z-50 overflow-y-auto"
        >
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/60 backdrop-blur-sm"
          />

          {/* Modal Container */}
          <div className="flex min-h-full items-center justify-center p-4">
            <Dialog.Panel
              as={motion.div}
              initial={{ opacity: 0, scale: 0.8, y: 50 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.8, y: 50 }}
              transition={{ type: "spring", stiffness: 300, damping: 30 }}
              className="relative w-full max-w-2xl transform overflow-hidden rounded-2xl bg-white shadow-2xl"
            >
              {/* Header */}
              <div className="bg-gradient-to-r from-primary-600 to-primary-700 px-6 py-4 text-white">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <CalendarDaysIcon className="h-6 w-6" />
                    <Dialog.Title className="text-lg font-semibold">
                      Agendar Reunião
                    </Dialog.Title>
                  </div>
                  <button
                    onClick={handleClose}
                    className="rounded-full p-1 hover:bg-white/20 transition-colors"
                  >
                    <XMarkIcon className="h-5 w-5" />
                  </button>
                </div>
                <p className="mt-1 text-sm text-primary-100">
                  {businessType} • {industry}
                </p>
              </div>

              {/* Form */}
              <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-6">
                {/* Meeting Type Selection */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-3">
                    Tipo de Reunião *
                  </label>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                    {meetingTypes.map((type) => {
                      const Icon = type.icon;
                      return (
                        <motion.div
                          key={type.id}
                          whileHover={{ scale: 1.02 }}
                          whileTap={{ scale: 0.98 }}
                        >
                          <label className="cursor-pointer">
                            <input
                              {...register('meetingType', { required: 'Tipo de reunião é obrigatório' })}
                              type="radio"
                              value={type.id}
                              className="sr-only"
                              onChange={() => setSelectedType(type.id)}
                            />
                            <div className={`p-4 border-2 rounded-lg transition-all ${
                              watchedMeetingType === type.id
                                ? 'border-primary-500 bg-primary-50'
                                : 'border-gray-200 hover:border-gray-300'
                            }`}>
                              <div className={`w-8 h-8 rounded-full flex items-center justify-center mb-2 ${type.color}`}>
                                <Icon className="w-4 h-4" />
                              </div>
                              <h4 className="font-medium text-gray-900">{type.name}</h4>
                              <p className="text-sm text-gray-600 mt-1">{type.duration}</p>
                              <p className="text-xs text-gray-500 mt-1">{type.description}</p>
                            </div>
                          </label>
                        </motion.div>
                      );
                    })}
                  </div>
                  {errors.meetingType && (
                    <p className="mt-1 text-sm text-red-600">{errors.meetingType.message}</p>
                  )}
                </div>

                {/* Client Information */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Name Field */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Nome completo *
                    </label>
                    <div className="relative">
                      <UserIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                      <input
                        {...register('clientName', {
                          required: 'Nome é obrigatório',
                          minLength: {
                            value: 2,
                            message: 'Nome deve ter pelo menos 2 caracteres'
                          }
                        })}
                        type="text"
                        placeholder="Seu nome completo"
                        className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors ${
                          errors.clientName ? 'border-red-300' : 'border-gray-300'
                        }`}
                      />
                    </div>
                    {errors.clientName && (
                      <p className="mt-1 text-sm text-red-600">{errors.clientName.message}</p>
                    )}
                  </div>

                  {/* Email Field */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Email *
                    </label>
                    <div className="relative">
                      <EnvelopeIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                      <input
                        {...register('clientEmail', {
                          required: 'Email é obrigatório',
                          pattern: {
                            value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                            message: 'Email deve ter um formato válido'
                          }
                        })}
                        type="email"
                        placeholder="<EMAIL>"
                        className={`w-full pl-10 pr-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors ${
                          errors.clientEmail ? 'border-red-300' : 'border-gray-300'
                        }`}
                      />
                    </div>
                    {errors.clientEmail && (
                      <p className="mt-1 text-sm text-red-600">{errors.clientEmail.message}</p>
                    )}
                  </div>
                </div>

                {/* Date and Time */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* Date Field */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Data preferida *
                    </label>
                    <input
                      {...register('preferredDate', {
                        required: 'Data é obrigatória'
                      })}
                      type="date"
                      min={getMinDate()}
                      max={getMaxDate()}
                      className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors ${
                        errors.preferredDate ? 'border-red-300' : 'border-gray-300'
                      }`}
                    />
                    {errors.preferredDate && (
                      <p className="mt-1 text-sm text-red-600">{errors.preferredDate.message}</p>
                    )}
                  </div>

                  {/* Time Field */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Horário preferido *
                    </label>
                    <input
                      {...register('preferredTime', {
                        required: 'Horário é obrigatório'
                      })}
                      type="time"
                      min="09:00"
                      max="18:00"
                      step="1800" // 30 minute intervals
                      className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors ${
                        errors.preferredTime ? 'border-red-300' : 'border-gray-300'
                      }`}
                    />
                    {errors.preferredTime && (
                      <p className="mt-1 text-sm text-red-600">{errors.preferredTime.message}</p>
                    )}
                    <p className="mt-1 text-xs text-gray-500">
                      Horário comercial: 9h às 18h (horário de Brasília)
                    </p>
                  </div>
                </div>

                {/* Notes Field */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Observações (opcional)
                  </label>
                  <textarea
                    {...register('notes')}
                    rows={3}
                    placeholder="Alguma informação adicional que gostaria de compartilhar..."
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors resize-none"
                  />
                </div>

                {/* Submit Button */}
                <motion.button
                  type="submit"
                  disabled={!isValid || isSubmitting}
                  className={`w-full py-3 px-4 rounded-lg font-semibold text-white transition-all duration-200 ${
                    isValid && !isSubmitting
                      ? 'bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 shadow-lg hover:shadow-xl'
                      : 'bg-gray-400 cursor-not-allowed'
                  }`}
                  whileHover={isValid && !isSubmitting ? { scale: 1.02 } : {}}
                  whileTap={isValid && !isSubmitting ? { scale: 0.98 } : {}}
                >
                  {isSubmitting ? (
                    <div className="flex items-center justify-center space-x-2">
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      <span>Agendando...</span>
                    </div>
                  ) : (
                    '📅 Agendar Reunião'
                  )}
                </motion.button>
              </form>
            </Dialog.Panel>
          </div>
        </Dialog>
      )}
    </AnimatePresence>
  );
};
