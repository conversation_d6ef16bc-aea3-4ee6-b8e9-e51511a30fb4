import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Dialog } from '@headlessui/react';
import { 
  XMarkIcon, 
  CreditCardIcon,
  BanknotesIcon,
  BuildingLibraryIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';
import { useForm } from 'react-hook-form';
import { toast } from 'react-hot-toast';
import { loadStripe } from '@stripe/stripe-js';
import { Elements, CardElement, useStripe, useElements } from '@stripe/react-stripe-js';
import { paymentApi } from '../../services/paymentApi';

interface PaymentFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: (paymentId: string) => void;
  analysisId: string;
  amount: number;
  currency: string;
  description: string;
  customerName: string;
  customerEmail: string;
}

interface PaymentFormData {
  paymentMethod: 'stripe' | 'pix' | 'bank_transfer';
  customerName: string;
  customerEmail: string;
  acceptTerms: boolean;
}

// Initialize Stripe
const stripePromise = loadStripe(import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY || '');

export const PaymentForm: React.FC<PaymentFormProps> = ({
  isOpen,
  onClose,
  onSuccess,
  analysisId,
  amount,
  currency,
  description,
  customerName,
  customerEmail
}) => {
  const [selectedMethod, setSelectedMethod] = useState<'stripe' | 'pix' | 'bank_transfer'>('stripe');
  const [isProcessing, setIsProcessing] = useState(false);
  const [paymentInstructions, setPaymentInstructions] = useState<any>(null);
  const [showInstructions, setShowInstructions] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors, isValid },
    reset,
    setValue
  } = useForm<PaymentFormData>({
    mode: 'onChange',
    defaultValues: {
      paymentMethod: 'stripe',
      customerName,
      customerEmail,
      acceptTerms: false
    }
  });

  // Set default values when props change
  useEffect(() => {
    setValue('customerName', customerName);
    setValue('customerEmail', customerEmail);
  }, [customerName, customerEmail, setValue]);

  const handleClose = () => {
    reset();
    setShowInstructions(false);
    setPaymentInstructions(null);
    onClose();
  };

  const formatAmount = (value: number, curr: string) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: curr.toUpperCase()
    }).format(value);
  };

  const onSubmit = async (data: PaymentFormData) => {
    if (!analysisId) {
      toast.error('ID da análise não encontrado. Recarregue a página e tente novamente.');
      return;
    }

    setIsProcessing(true);

    try {
      const response = await paymentApi.createPayment({
        analysisId,
        amount,
        currency,
        description,
        customerName: data.customerName,
        customerEmail: data.customerEmail,
        paymentMethod: data.paymentMethod
      });

      if (data.paymentMethod === 'stripe' && response.data.clientSecret) {
        // Handle Stripe payment in child component
        return;
      } else {
        // Show manual payment instructions
        setPaymentInstructions(response.data.instructions);
        setShowInstructions(true);
        toast.success('Instruções de pagamento geradas! 📋');
      }

    } catch (error: any) {
      console.error('Error creating payment:', error);
      toast.error(
        error.response?.data?.error || 
        'Erro ao processar pagamento. Tente novamente.'
      );
    } finally {
      setIsProcessing(false);
    }
  };

  const paymentMethods = [
    {
      id: 'stripe' as const,
      name: 'Cartão de Crédito/Débito',
      description: 'Pagamento seguro e instantâneo',
      icon: CreditCardIcon,
      color: 'bg-blue-100 text-blue-600',
      available: !!import.meta.env.VITE_STRIPE_PUBLISHABLE_KEY
    },
    {
      id: 'pix' as const,
      name: 'PIX',
      description: 'Transferência instantânea (Brasil)',
      icon: BanknotesIcon,
      color: 'bg-green-100 text-green-600',
      available: currency.toUpperCase() === 'BRL'
    },
    {
      id: 'bank_transfer' as const,
      name: 'Transferência Bancária',
      description: 'Transferência tradicional',
      icon: BuildingLibraryIcon,
      color: 'bg-purple-100 text-purple-600',
      available: true
    }
  ];

  const availableMethods = paymentMethods.filter(method => method.available);

  return (
    <AnimatePresence>
      {isOpen && (
        <Dialog
          as={motion.div}
          static
          open={isOpen}
          onClose={handleClose}
          className="fixed inset-0 z-50 overflow-y-auto"
        >
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/60 backdrop-blur-sm"
          />

          {/* Modal Container */}
          <div className="flex min-h-full items-center justify-center p-4">
            <Dialog.Panel
              as={motion.div}
              initial={{ opacity: 0, scale: 0.8, y: 50 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.8, y: 50 }}
              transition={{ type: "spring", stiffness: 300, damping: 30 }}
              className="relative w-full max-w-2xl transform overflow-hidden rounded-2xl bg-white shadow-2xl"
            >
              {/* Header */}
              <div className="bg-gradient-to-r from-primary-600 to-primary-700 px-6 py-4 text-white">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <CreditCardIcon className="h-6 w-6" />
                    <Dialog.Title className="text-lg font-semibold">
                      Finalizar Pagamento
                    </Dialog.Title>
                  </div>
                  <button
                    onClick={handleClose}
                    className="rounded-full p-1 hover:bg-white/20 transition-colors"
                  >
                    <XMarkIcon className="h-5 w-5" />
                  </button>
                </div>
                <p className="mt-1 text-sm text-primary-100">
                  {description}
                </p>
              </div>

              {/* Content */}
              <div className="p-6">
                {!showInstructions ? (
                  <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                    {/* Payment Summary */}
                    <div className="bg-gray-50 rounded-lg p-4">
                      <h3 className="font-semibold text-gray-900 mb-2">Resumo do Pagamento</h3>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-600">{description}</span>
                        <span className="text-2xl font-bold text-primary-600">
                          {formatAmount(amount, currency)}
                        </span>
                      </div>
                    </div>

                    {/* Payment Method Selection */}
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-3">
                        Método de Pagamento *
                      </label>
                      <div className="grid grid-cols-1 gap-3">
                        {availableMethods.map((method) => {
                          const Icon = method.icon;
                          return (
                            <motion.div
                              key={method.id}
                              whileHover={{ scale: 1.02 }}
                              whileTap={{ scale: 0.98 }}
                            >
                              <label className="cursor-pointer">
                                <input
                                  {...register('paymentMethod', { required: 'Método de pagamento é obrigatório' })}
                                  type="radio"
                                  value={method.id}
                                  className="sr-only"
                                  onChange={() => setSelectedMethod(method.id)}
                                />
                                <div className={`p-4 border-2 rounded-lg transition-all ${
                                  selectedMethod === method.id
                                    ? 'border-primary-500 bg-primary-50'
                                    : 'border-gray-200 hover:border-gray-300'
                                }`}>
                                  <div className="flex items-center space-x-3">
                                    <div className={`w-10 h-10 rounded-full flex items-center justify-center ${method.color}`}>
                                      <Icon className="w-5 h-5" />
                                    </div>
                                    <div className="flex-1">
                                      <h4 className="font-medium text-gray-900">{method.name}</h4>
                                      <p className="text-sm text-gray-600">{method.description}</p>
                                    </div>
                                    {selectedMethod === method.id && (
                                      <CheckCircleIcon className="w-5 h-5 text-primary-600" />
                                    )}
                                  </div>
                                </div>
                              </label>
                            </motion.div>
                          );
                        })}
                      </div>
                    </div>

                    {/* Stripe Card Element */}
                    {selectedMethod === 'stripe' && (
                      <Elements stripe={stripePromise}>
                        <StripeCardForm
                          amount={amount}
                          currency={currency}
                          description={description}
                          customerName={customerName}
                          customerEmail={customerEmail}
                          analysisId={analysisId}
                          onSuccess={onSuccess}
                          onError={(error) => toast.error(error)}
                          isProcessing={isProcessing}
                          setIsProcessing={setIsProcessing}
                        />
                      </Elements>
                    )}

                    {/* Customer Information */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Nome completo *
                        </label>
                        <input
                          {...register('customerName', {
                            required: 'Nome é obrigatório',
                            minLength: {
                              value: 2,
                              message: 'Nome deve ter pelo menos 2 caracteres'
                            }
                          })}
                          type="text"
                          className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors ${
                            errors.customerName ? 'border-red-300' : 'border-gray-300'
                          }`}
                        />
                        {errors.customerName && (
                          <p className="mt-1 text-sm text-red-600">{errors.customerName.message}</p>
                        )}
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          Email *
                        </label>
                        <input
                          {...register('customerEmail', {
                            required: 'Email é obrigatório',
                            pattern: {
                              value: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
                              message: 'Email deve ter um formato válido'
                            }
                          })}
                          type="email"
                          className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors ${
                            errors.customerEmail ? 'border-red-300' : 'border-gray-300'
                          }`}
                        />
                        {errors.customerEmail && (
                          <p className="mt-1 text-sm text-red-600">{errors.customerEmail.message}</p>
                        )}
                      </div>
                    </div>

                    {/* Terms and Conditions */}
                    <div className="flex items-start space-x-2">
                      <input
                        {...register('acceptTerms', {
                          required: 'Você deve aceitar os termos e condições'
                        })}
                        type="checkbox"
                        className="mt-1 rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                      />
                      <label className="text-sm text-gray-600">
                        Aceito os{' '}
                        <a href="/terms" target="_blank" className="text-primary-600 hover:underline">
                          termos e condições
                        </a>{' '}
                        e{' '}
                        <a href="/privacy" target="_blank" className="text-primary-600 hover:underline">
                          política de privacidade
                        </a>
                      </label>
                    </div>
                    {errors.acceptTerms && (
                      <p className="text-sm text-red-600">{errors.acceptTerms.message}</p>
                    )}

                    {/* Submit Button (for non-Stripe methods) */}
                    {selectedMethod !== 'stripe' && (
                      <motion.button
                        type="submit"
                        disabled={!isValid || isProcessing}
                        className={`w-full py-3 px-4 rounded-lg font-semibold text-white transition-all duration-200 ${
                          isValid && !isProcessing
                            ? 'bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 shadow-lg hover:shadow-xl'
                            : 'bg-gray-400 cursor-not-allowed'
                        }`}
                        whileHover={isValid && !isProcessing ? { scale: 1.02 } : {}}
                        whileTap={isValid && !isProcessing ? { scale: 0.98 } : {}}
                      >
                        {isProcessing ? (
                          <div className="flex items-center justify-center space-x-2">
                            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                            <span>Processando...</span>
                          </div>
                        ) : (
                          '💳 Gerar Instruções de Pagamento'
                        )}
                      </motion.button>
                    )}
                  </form>
                ) : (
                  <PaymentInstructions
                    instructions={paymentInstructions}
                    onClose={handleClose}
                    onSuccess={() => onSuccess('manual')}
                  />
                )}
              </div>
            </Dialog.Panel>
          </div>
        </Dialog>
      )}
    </AnimatePresence>
  );
};

// Stripe Card Form Component
interface StripeCardFormProps {
  amount: number;
  currency: string;
  description: string;
  customerName: string;
  customerEmail: string;
  analysisId: string;
  onSuccess: (paymentId: string) => void;
  onError: (error: string) => void;
  isProcessing: boolean;
  setIsProcessing: (processing: boolean) => void;
}

const StripeCardForm: React.FC<StripeCardFormProps> = ({
  amount,
  currency,
  description,
  customerName,
  customerEmail,
  analysisId,
  onSuccess,
  onError,
  isProcessing,
  setIsProcessing
}) => {
  const stripe = useStripe();
  const elements = useElements();

  const handleStripePayment = async () => {
    if (!stripe || !elements) {
      onError('Stripe não foi carregado corretamente');
      return;
    }

    const cardElement = elements.getElement(CardElement);
    if (!cardElement) {
      onError('Elemento do cartão não encontrado');
      return;
    }

    setIsProcessing(true);

    try {
      // Create payment intent
      const response = await paymentApi.createPayment({
        analysisId,
        amount,
        currency,
        description,
        customerName,
        customerEmail,
        paymentMethod: 'stripe'
      });

      if (!response.data.clientSecret) {
        throw new Error('Client secret não recebido');
      }

      // Confirm payment
      const { error, paymentIntent } = await stripe.confirmCardPayment(
        response.data.clientSecret,
        {
          payment_method: {
            card: cardElement,
            billing_details: {
              name: customerName,
              email: customerEmail
            }
          }
        }
      );

      if (error) {
        onError(error.message || 'Erro no pagamento');
      } else if (paymentIntent?.status === 'succeeded') {
        onSuccess(response.data.paymentId);
        toast.success('Pagamento realizado com sucesso! 🎉');
      }
    } catch (error: any) {
      onError(error.response?.data?.error || 'Erro ao processar pagamento');
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Informações do Cartão *
        </label>
        <div className="p-4 border border-gray-300 rounded-lg">
          <CardElement
            options={{
              style: {
                base: {
                  fontSize: '16px',
                  color: '#424770',
                  '::placeholder': {
                    color: '#aab7c4',
                  },
                },
              },
            }}
          />
        </div>
      </div>

      <motion.button
        type="button"
        onClick={handleStripePayment}
        disabled={!stripe || isProcessing}
        className={`w-full py-3 px-4 rounded-lg font-semibold text-white transition-all duration-200 ${
          stripe && !isProcessing
            ? 'bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 shadow-lg hover:shadow-xl'
            : 'bg-gray-400 cursor-not-allowed'
        }`}
        whileHover={stripe && !isProcessing ? { scale: 1.02 } : {}}
        whileTap={stripe && !isProcessing ? { scale: 0.98 } : {}}
      >
        {isProcessing ? (
          <div className="flex items-center justify-center space-x-2">
            <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
            <span>Processando Pagamento...</span>
          </div>
        ) : (
          `💳 Pagar ${new Intl.NumberFormat('pt-BR', {
            style: 'currency',
            currency: currency.toUpperCase()
          }).format(amount)}`
        )}
      </motion.button>
    </div>
  );
};

// Payment Instructions Component
interface PaymentInstructionsProps {
  instructions: any;
  onClose: () => void;
  onSuccess: () => void;
}

const PaymentInstructions: React.FC<PaymentInstructionsProps> = ({
  instructions,
  onClose,
  onSuccess
}) => {
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success('Copiado para a área de transferência!');
  };

  return (
    <div className="space-y-6">
      <div className="text-center">
        <CheckCircleIcon className="w-16 h-16 text-green-500 mx-auto mb-4" />
        <h3 className="text-xl font-semibold text-gray-900 mb-2">
          Instruções de Pagamento Geradas
        </h3>
        <p className="text-gray-600">
          Siga as instruções abaixo para completar seu pagamento
        </p>
      </div>

      <div className="bg-gray-50 rounded-lg p-6">
        <h4 className="font-semibold text-gray-900 mb-4">
          {instructions.type === 'pix' && '📱 Pagamento via PIX'}
          {instructions.type === 'bank_transfer' && '🏦 Transferência Bancária'}
          {instructions.type === 'manual' && '📞 Pagamento Manual'}
        </h4>

        <p className="text-gray-700 mb-4">{instructions.message}</p>
        <p className="text-lg font-bold text-primary-600 mb-4">
          Valor: {instructions.amount}
        </p>

        {instructions.type === 'pix' && (
          <div className="space-y-2">
            <p className="text-sm font-medium text-gray-700">Chave PIX:</p>
            <div className="flex items-center space-x-2">
              <code className="flex-1 bg-white p-2 rounded border text-sm">
                {instructions.pixKey}
              </code>
              <button
                onClick={() => copyToClipboard(instructions.pixKey)}
                className="px-3 py-2 bg-primary-600 text-white rounded hover:bg-primary-700 text-sm"
              >
                Copiar
              </button>
            </div>
          </div>
        )}

        {instructions.type === 'bank_transfer' && instructions.bankDetails && (
          <div className="space-y-2 text-sm">
            <p><strong>Banco:</strong> {instructions.bankDetails.bank}</p>
            <p><strong>Agência:</strong> {instructions.bankDetails.agency}</p>
            <p><strong>Conta:</strong> {instructions.bankDetails.account}</p>
            <p><strong>Favorecido:</strong> {instructions.bankDetails.accountHolder}</p>
            <p><strong>CNPJ:</strong> {instructions.bankDetails.cnpj}</p>
          </div>
        )}
      </div>

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-start space-x-2">
          <ExclamationTriangleIcon className="w-5 h-5 text-blue-600 mt-0.5" />
          <div className="text-sm text-blue-800">
            <p className="font-medium mb-1">Importante:</p>
            <ul className="list-disc list-inside space-y-1">
              <li>Após realizar o pagamento, envie o comprovante para nosso email</li>
              <li>O processamento pode levar até 2 dias úteis</li>
              <li>Você receberá uma confirmação por email quando o pagamento for processado</li>
            </ul>
          </div>
        </div>
      </div>

      <div className="flex space-x-4">
        <button
          onClick={onClose}
          className="flex-1 py-2 px-4 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
        >
          Fechar
        </button>
        <button
          onClick={onSuccess}
          className="flex-1 py-2 px-4 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
        >
          Pagamento Realizado
        </button>
      </div>
    </div>
  );
};
