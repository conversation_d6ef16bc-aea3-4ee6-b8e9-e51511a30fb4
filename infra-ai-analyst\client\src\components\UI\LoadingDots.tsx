import React from 'react';
import { motion } from 'framer-motion';

interface LoadingDotsProps {
  size?: 'sm' | 'md' | 'lg';
  color?: string;
}

export const LoadingDots: React.FC<LoadingDotsProps> = ({ 
  size = 'sm', 
  color = 'bg-gray-400' 
}) => {
  const sizeClasses = {
    sm: 'w-1 h-1',
    md: 'w-2 h-2',
    lg: 'w-3 h-3'
  };

  const dotVariants = {
    initial: { y: 0 },
    animate: { y: -4 }
  };

  const containerVariants = {
    initial: {},
    animate: {
      transition: {
        staggerChildren: 0.2,
        repeat: Infinity,
        repeatType: "reverse" as const,
        duration: 0.6
      }
    }
  };

  return (
    <motion.div
      className="flex items-center space-x-1"
      variants={containerVariants}
      initial="initial"
      animate="animate"
    >
      {[0, 1, 2].map((index) => (
        <motion.div
          key={index}
          className={`${sizeClasses[size]} ${color} rounded-full`}
          variants={dotVariants}
          transition={{
            duration: 0.3,
            ease: "easeInOut"
          }}
        />
      ))}
    </motion.div>
  );
};
