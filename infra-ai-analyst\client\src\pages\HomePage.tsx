import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import {
  ChatBubbleLeftRightIcon,
  DocumentTextIcon,
  CogIcon,
  CalendarDaysIcon,
  CreditCardIcon,
  ChartBarIcon,
  CheckCircleIcon,
  ArrowRightIcon
} from '@heroicons/react/24/outline';

const HomePage: React.FC = () => {
  const features = [
    {
      icon: ChatBubbleLeftRightIcon,
      title: 'Chat Inteligente',
      description: 'Converse com nossa IA especializada em análise de negócios e receba insights personalizados.'
    },
    {
      icon: DocumentTextIcon,
      title: 'Análises Detalhadas',
      description: 'Relatórios completos com blueprint, roadmap e plano de implementação para seu negócio.'
    },
    {
      icon: CogIcon,
      title: 'Automação N8N',
      description: 'Templates personalizados de automação prontos para implementar em sua empresa.'
    },
    {
      icon: CalendarDaysIcon,
      title: 'Agendamento Integrado',
      description: 'Agende reuniões de consultoria diretamente na plataforma com integração ao Google Calendar.'
    },
    {
      icon: CreditCardIcon,
      title: 'Pagamentos Seguros',
      description: 'Sistema de pagamentos integrado com Stripe, PIX e transferência bancária.'
    },
    {
      icon: ChartBarIcon,
      title: 'Dashboard Administrativo',
      description: 'Painel completo para acompanhar métricas, conversões e performance do sistema.'
    }
  ];

  const benefits = [
    'Análise personalizada baseada em IA',
    'Templates de automação prontos para uso',
    'Consultoria especializada incluída',
    'Implementação guiada passo-a-passo',
    'Suporte técnico completo',
    'ROI garantido em 90 dias'
  ];

  const stats = [
    { label: 'Empresas Atendidas', value: '500+' },
    { label: 'Processos Automatizados', value: '2.5k+' },
    { label: 'Horas Economizadas', value: '10k+' },
    { label: 'ROI Médio', value: '300%' }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-primary-600 via-primary-700 to-primary-800 text-white overflow-hidden">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
          <div className="text-center">
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8 }}
              className="text-4xl md:text-6xl font-bold mb-6"
            >
              Transforme seu Negócio com
              <span className="block text-transparent bg-clip-text bg-gradient-to-r from-yellow-400 to-orange-500">
                Inteligência Artificial
              </span>
            </motion.h1>
            
            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="text-xl md:text-2xl text-primary-100 mb-8 max-w-3xl mx-auto"
            >
              Análises personalizadas, automação inteligente e consultoria especializada 
              para acelerar o crescimento da sua empresa.
            </motion.p>
            
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="flex flex-col sm:flex-row gap-4 justify-center"
            >
              <Link
                to="/chat"
                className="inline-flex items-center px-8 py-4 bg-white text-primary-700 rounded-lg font-semibold text-lg hover:bg-gray-50 transition-all shadow-lg hover:shadow-xl transform hover:scale-105"
              >
                <ChatBubbleLeftRightIcon className="w-6 h-6 mr-2" />
                Começar Análise Gratuita
              </Link>
              
              <a
                href="#features"
                className="inline-flex items-center px-8 py-4 border-2 border-white text-white rounded-lg font-semibold text-lg hover:bg-white hover:text-primary-700 transition-all"
              >
                Saiba Mais
                <ArrowRightIcon className="w-6 h-6 ml-2" />
              </a>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <motion.div
                key={stat.label}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className="text-center"
              >
                <div className="text-3xl md:text-4xl font-bold text-primary-600 mb-2">
                  {stat.value}
                </div>
                <div className="text-gray-600 font-medium">
                  {stat.label}
                </div>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              Plataforma Completa de Automação
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Tudo que você precisa para analisar, automatizar e otimizar 
              os processos da sua empresa em uma única plataforma.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => {
              const Icon = feature.icon;
              return (
                <motion.div
                  key={feature.title}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow border border-gray-100"
                >
                  <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mb-4">
                    <Icon className="w-6 h-6 text-primary-600" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">
                    {feature.title}
                  </h3>
                  <p className="text-gray-600">
                    {feature.description}
                  </p>
                </motion.div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
            >
              <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Por que escolher a Infra AI Analyst?
              </h2>
              <p className="text-xl text-gray-600 mb-8">
                Nossa plataforma combina inteligência artificial avançada com 
                expertise em automação para entregar resultados excepcionais.
              </p>
              
              <div className="space-y-4">
                {benefits.map((benefit, index) => (
                  <motion.div
                    key={benefit}
                    initial={{ opacity: 0, x: -20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="flex items-center space-x-3"
                  >
                    <CheckCircleIcon className="w-6 h-6 text-green-500 flex-shrink-0" />
                    <span className="text-gray-700 font-medium">{benefit}</span>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8 }}
              viewport={{ once: true }}
              className="relative"
            >
              <div className="bg-gradient-to-br from-primary-600 to-primary-700 rounded-2xl p-8 text-white">
                <h3 className="text-2xl font-bold mb-4">
                  Comece sua transformação hoje
                </h3>
                <p className="text-primary-100 mb-6">
                  Análise gratuita do seu negócio em menos de 10 minutos. 
                  Descubra oportunidades de automação personalizadas.
                </p>
                <Link
                  to="/chat"
                  className="inline-flex items-center px-6 py-3 bg-white text-primary-700 rounded-lg font-semibold hover:bg-gray-50 transition-colors"
                >
                  Iniciar Análise Gratuita
                  <ArrowRightIcon className="w-5 h-5 ml-2" />
                </Link>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-primary-600">
        <div className="max-w-4xl mx-auto text-center px-4 sm:px-6 lg:px-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
              Pronto para revolucionar seu negócio?
            </h2>
            <p className="text-xl text-primary-100 mb-8">
              Junte-se a centenas de empresas que já transformaram seus processos 
              com nossa plataforma de automação inteligente.
            </p>
            <Link
              to="/chat"
              className="inline-flex items-center px-8 py-4 bg-white text-primary-700 rounded-lg font-semibold text-lg hover:bg-gray-50 transition-all shadow-lg hover:shadow-xl transform hover:scale-105"
            >
              <ChatBubbleLeftRightIcon className="w-6 h-6 mr-2" />
              Começar Agora - É Gratuito
            </Link>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default HomePage;
