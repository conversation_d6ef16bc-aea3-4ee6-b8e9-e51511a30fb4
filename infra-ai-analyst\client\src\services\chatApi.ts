import axios from 'axios';
import { Message } from '../components/Chatbot/ChatMessage';
import { BusinessData } from '../store/chatStore';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

// Create axios instance with default config
const chatApiClient = axios.create({
  baseURL: `${API_BASE_URL}/chat`,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for logging
chatApiClient.interceptors.request.use(
  (config) => {
    console.log(`[Chat API] ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('[Chat API] Request error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
chatApiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    console.error('[Chat API] Response error:', error.response?.data || error.message);
    
    // Handle specific error cases
    if (error.response?.status === 429) {
      throw new Error('Muitas mensagens enviadas. Aguarde um momento antes de tentar novamente.');
    } else if (error.response?.status === 404) {
      throw new Error('Sessão de chat não encontrada.');
    } else if (error.response?.status >= 500) {
      throw new Error('Erro interno do servidor. Tente novamente em alguns instantes.');
    }
    
    return Promise.reject(error);
  }
);

export interface StartSessionResponse {
  success: boolean;
  data: {
    sessionId: string;
    message: string;
    timestamp: string;
  };
}

export interface SendMessageResponse {
  success: boolean;
  data: {
    message: string;
    timestamp: string;
    analysis: {
      readinessScore: number;
      isReady: boolean;
      collectedData: BusinessData;
    };
  };
}

export interface ChatHistoryResponse {
  success: boolean;
  data: {
    sessionId: string;
    status: string;
    messages: Message[];
    analysis: {
      readinessScore: number;
      isReady: boolean;
      collectedData: BusinessData;
    };
    createdAt: string;
  };
}

export interface SessionStatusResponse {
  success: boolean;
  data: {
    sessionId: string;
    status: string;
    messageCount: number;
    analysis: {
      readinessScore: number;
      isReady: boolean;
    };
    createdAt: string;
    updatedAt: string;
  };
}

export interface CompleteSessionResponse {
  success: boolean;
  data: {
    sessionId: string;
    status: string;
    businessData: BusinessData;
    messageCount: number;
  };
}

export const chatApi = {
  // Start a new chat session
  async startSession(): Promise<StartSessionResponse> {
    const response = await chatApiClient.post('/start');
    return response.data;
  },

  // Send a message to the chat
  async sendMessage(message: string, sessionId: string): Promise<SendMessageResponse> {
    const response = await chatApiClient.post('/message', {
      message,
      sessionId
    });
    return response.data;
  },

  // Get chat history for a session
  async getChatHistory(sessionId: string): Promise<ChatHistoryResponse> {
    const response = await chatApiClient.get(`/${sessionId}/history`);
    return response.data;
  },

  // Get session status
  async getSessionStatus(sessionId: string): Promise<SessionStatusResponse> {
    const response = await chatApiClient.get(`/${sessionId}/status`);
    return response.data;
  },

  // Complete a chat session
  async completeSession(sessionId: string): Promise<CompleteSessionResponse> {
    const response = await chatApiClient.post(`/${sessionId}/complete`);
    return response.data;
  },

  // Health check
  async healthCheck(): Promise<{ success: boolean; service: string; status: string }> {
    const response = await chatApiClient.get('/health');
    return response.data;
  }
};

export default chatApi;
