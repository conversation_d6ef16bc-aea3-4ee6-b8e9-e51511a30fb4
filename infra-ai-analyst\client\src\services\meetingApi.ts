import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

// Create axios instance with default config
const meetingApiClient = axios.create({
  baseURL: `${API_BASE_URL}/meetings`,
  timeout: 15000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for logging
meetingApiClient.interceptors.request.use(
  (config) => {
    console.log(`[Meeting API] ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('[Meeting API] Request error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
meetingApiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    console.error('[Meeting API] Response error:', error.response?.data || error.message);
    
    // Handle specific error cases
    if (error.response?.status === 429) {
      throw new Error('Muitas tentativas de agendamento. Aguarde um momento antes de tentar novamente.');
    } else if (error.response?.status === 400) {
      throw new Error(error.response.data?.error || 'Dados inválidos fornecidos.');
    } else if (error.response?.status === 404) {
      throw new Error('Reunião ou análise não encontrada.');
    } else if (error.response?.status >= 500) {
      throw new Error('Erro interno do servidor. Tente novamente em alguns instantes.');
    }
    
    return Promise.reject(error);
  }
);

export interface ScheduleMeetingData {
  analysisId: string;
  clientName: string;
  clientEmail: string;
  preferredDate: string;
  preferredTime: string;
  timezone?: string;
  meetingType: 'consultation' | 'implementation' | 'follow-up';
  notes?: string;
}

export interface ScheduleMeetingResponse {
  success: boolean;
  data: {
    meetingId: string;
    scheduledAt: string;
    duration: number;
    meetingUrl?: string;
    googleEventId?: string;
    message: string;
  };
}

export interface MeetingData {
  id: string;
  title: string;
  description?: string;
  scheduledAt: string;
  duration: number;
  clientName: string;
  clientEmail: string;
  meetingType: 'consultation' | 'implementation' | 'follow-up';
  timezone: string;
  status: 'scheduled' | 'completed' | 'cancelled' | 'rescheduled';
  meetingUrl?: string;
  googleEventId?: string;
  notes?: string;
  analysis?: {
    id: string;
    businessType: string;
    industry: string;
    user?: {
      id: string;
      name: string;
      email: string;
      company?: string;
    };
  };
  createdAt: string;
  updatedAt: string;
}

export interface GetMeetingResponse {
  success: boolean;
  data: MeetingData;
}

export interface GetAllMeetingsResponse {
  success: boolean;
  data: {
    meetings: Array<{
      id: string;
      title: string;
      scheduledAt: string;
      duration: number;
      clientName: string;
      clientEmail: string;
      meetingType: string;
      status: string;
      meetingUrl?: string;
      analysis?: {
        id: string;
        businessType: string;
        industry: string;
        user?: {
          name: string;
          email: string;
          company?: string;
        };
      };
      createdAt: string;
    }>;
    pagination: {
      page: number;
      limit: number;
      total: number;
      pages: number;
    };
  };
}

export interface UpdateMeetingData {
  status?: 'scheduled' | 'completed' | 'cancelled' | 'rescheduled';
  scheduledAt?: string;
  notes?: string;
  meetingUrl?: string;
}

export interface UpdateMeetingResponse {
  success: boolean;
  data: MeetingData;
}

export const meetingApi = {
  // Schedule a new meeting (critical flow)
  async scheduleMeeting(data: ScheduleMeetingData): Promise<ScheduleMeetingResponse> {
    const response = await meetingApiClient.post('/schedule', data);
    return response.data;
  },

  // Get meeting details
  async getMeeting(meetingId: string): Promise<GetMeetingResponse> {
    const response = await meetingApiClient.get(`/${meetingId}`);
    return response.data;
  },

  // Get all meetings (admin only)
  async getAllMeetings(params?: {
    page?: number;
    limit?: number;
    status?: string;
    meetingType?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }): Promise<GetAllMeetingsResponse> {
    const response = await meetingApiClient.get('/', { params });
    return response.data;
  },

  // Update meeting (admin only)
  async updateMeeting(meetingId: string, data: UpdateMeetingData): Promise<UpdateMeetingResponse> {
    const response = await meetingApiClient.put(`/${meetingId}`, data);
    return response.data;
  },

  // Cancel meeting
  async cancelMeeting(meetingId: string): Promise<{ success: boolean; message: string }> {
    const response = await meetingApiClient.post(`/${meetingId}/cancel`);
    return response.data;
  },

  // Reschedule meeting
  async rescheduleMeeting(
    meetingId: string, 
    newDate: string, 
    newTime: string
  ): Promise<UpdateMeetingResponse> {
    const response = await meetingApiClient.post(`/${meetingId}/reschedule`, {
      preferredDate: newDate,
      preferredTime: newTime
    });
    return response.data;
  },

  // Get available time slots
  async getAvailableSlots(params?: {
    startDate?: string;
    endDate?: string;
    duration?: number;
    timezone?: string;
  }): Promise<{ success: boolean; data: { slots: string[] } }> {
    const response = await meetingApiClient.get('/available-slots', { params });
    return response.data;
  },

  // Health check
  async healthCheck(): Promise<{ success: boolean; service: string; status: string }> {
    const response = await meetingApiClient.get('/health');
    return response.data;
  }
};

export default meetingApi;
