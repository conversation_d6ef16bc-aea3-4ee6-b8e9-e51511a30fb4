import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

// Create axios instance with default config
const paymentApiClient = axios.create({
  baseURL: `${API_BASE_URL}/payments`,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor for logging
paymentApiClient.interceptors.request.use(
  (config) => {
    console.log(`[Payment API] ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    console.error('[Payment API] Request error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
paymentApiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    console.error('[Payment API] Response error:', error.response?.data || error.message);
    
    // Handle specific error cases
    if (error.response?.status === 429) {
      throw new Error('Muitas tentativas de pagamento. Aguarde um momento antes de tentar novamente.');
    } else if (error.response?.status === 400) {
      throw new Error(error.response.data?.error || 'Dados de pagamento inválidos.');
    } else if (error.response?.status === 404) {
      throw new Error('Pagamento ou análise não encontrada.');
    } else if (error.response?.status >= 500) {
      throw new Error('Erro interno do servidor. Tente novamente em alguns instantes.');
    }
    
    return Promise.reject(error);
  }
);

export interface CreatePaymentData {
  analysisId: string;
  amount: number;
  currency: string;
  description: string;
  customerEmail: string;
  customerName: string;
  paymentMethod?: 'stripe' | 'pix' | 'bank_transfer';
  metadata?: any;
}

export interface CreatePaymentResponse {
  success: boolean;
  data: {
    paymentId: string;
    amount: number;
    currency: string;
    status: string;
    paymentMethod: string;
    clientSecret?: string;
    stripePaymentIntentId?: string;
    instructions?: {
      type: string;
      message: string;
      amount: string;
      pixKey?: string;
      bankDetails?: {
        bank: string;
        agency: string;
        account: string;
        accountHolder: string;
        cnpj: string;
      };
    };
  };
}

export interface PaymentData {
  id: string;
  amount: number;
  currency: string;
  description: string;
  customerName: string;
  customerEmail: string;
  paymentMethod: string;
  status: 'pending' | 'completed' | 'failed' | 'cancelled' | 'refunded';
  stripePaymentIntentId?: string;
  metadata?: any;
  user?: {
    id: string;
    name: string;
    email: string;
    company?: string;
  };
  analysis?: {
    id: string;
    businessType: string;
    industry: string;
  };
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
}

export interface GetPaymentResponse {
  success: boolean;
  data: PaymentData;
}

export interface GetAllPaymentsResponse {
  success: boolean;
  data: {
    payments: Array<{
      id: string;
      amount: number;
      currency: string;
      description: string;
      customerName: string;
      customerEmail: string;
      paymentMethod: string;
      status: string;
      user?: {
        name: string;
        email: string;
        company?: string;
      };
      analysis?: {
        businessType: string;
        industry: string;
      };
      createdAt: string;
      completedAt?: string;
    }>;
    pagination: {
      page: number;
      limit: number;
      total: number;
      pages: number;
    };
  };
}

export interface PaymentMethod {
  id: string;
  name: string;
  description: string;
  icon: string;
  currencies: string[];
  processingTime: string;
}

export interface GetPaymentMethodsResponse {
  success: boolean;
  data: {
    methods: PaymentMethod[];
    defaultCurrency: string;
  };
}

export interface StripeConfigResponse {
  success: boolean;
  data: {
    publishableKey: string;
    currency: string;
    country: string;
  };
}

export const paymentApi = {
  // Create a new payment (critical flow)
  async createPayment(data: CreatePaymentData): Promise<CreatePaymentResponse> {
    const response = await paymentApiClient.post('/create', data);
    return response.data;
  },

  // Get payment details
  async getPayment(paymentId: string): Promise<GetPaymentResponse> {
    const response = await paymentApiClient.get(`/${paymentId}`);
    return response.data;
  },

  // Get all payments (admin only)
  async getAllPayments(params?: {
    page?: number;
    limit?: number;
    status?: string;
    paymentMethod?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }): Promise<GetAllPaymentsResponse> {
    const response = await paymentApiClient.get('/', { params });
    return response.data;
  },

  // Get Stripe configuration
  async getStripeConfig(): Promise<StripeConfigResponse> {
    const response = await paymentApiClient.get('/config/stripe');
    return response.data;
  },

  // Get available payment methods
  async getPaymentMethods(): Promise<GetPaymentMethodsResponse> {
    const response = await paymentApiClient.get('/methods/available');
    return response.data;
  },

  // Cancel payment
  async cancelPayment(paymentId: string): Promise<{ success: boolean; message: string }> {
    const response = await paymentApiClient.post(`/${paymentId}/cancel`);
    return response.data;
  },

  // Request refund (admin only)
  async requestRefund(
    paymentId: string, 
    amount?: number, 
    reason?: string
  ): Promise<{ success: boolean; data: any }> {
    const response = await paymentApiClient.post(`/${paymentId}/refund`, {
      amount,
      reason
    });
    return response.data;
  },

  // Update payment status (admin only)
  async updatePaymentStatus(
    paymentId: string, 
    status: string
  ): Promise<{ success: boolean; data: PaymentData }> {
    const response = await paymentApiClient.patch(`/${paymentId}/status`, {
      status
    });
    return response.data;
  },

  // Health check
  async healthCheck(): Promise<{ success: boolean; service: string; status: string }> {
    const response = await paymentApiClient.get('/health');
    return response.data;
  }
};

export default paymentApi;
