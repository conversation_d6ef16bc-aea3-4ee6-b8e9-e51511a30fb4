import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { chatApi } from '../services/chatApi';
import { Message } from '../components/Chatbot/ChatMessage';

export interface BusinessData {
  businessType?: string;
  industry?: string;
  companySize?: string;
  currentTools?: string[];
  painPoints?: string[];
  goals?: string[];
  budget?: string;
  timeline?: string;
  technicalLevel?: string;
}

interface ChatState {
  // Session state
  sessionId: string | null;
  isConnected: boolean;
  
  // Messages
  messages: Message[];
  isLoading: boolean;
  isTyping: boolean;
  
  // Analysis readiness
  readinessScore: number;
  isReady: boolean;
  businessData: BusinessData;
  
  // Error handling
  error: string | null;
  
  // Actions
  startSession: () => Promise<void>;
  sendMessage: (message: string, sessionId: string) => Promise<void>;
  loadChatHistory: (sessionId: string) => Promise<void>;
  completeSession: (sessionId: string) => Promise<void>;
  clearError: () => void;
  resetChat: () => void;
  
  // Real-time updates
  addMessage: (message: Message) => void;
  updateReadiness: (score: number, isReady: boolean, data?: BusinessData) => void;
}

export const useChatStore = create<ChatState>()(
  devtools(
    (set, get) => ({
      // Initial state
      sessionId: null,
      isConnected: false,
      messages: [],
      isLoading: false,
      isTyping: false,
      readinessScore: 0,
      isReady: false,
      businessData: {},
      error: null,

      // Start a new chat session
      startSession: async () => {
        try {
          set({ isLoading: true, error: null });
          
          const response = await chatApi.startSession();
          
          set({
            sessionId: response.data.sessionId,
            isConnected: true,
            messages: [{
              role: 'assistant',
              content: response.data.message,
              timestamp: response.data.timestamp
            }],
            isLoading: false
          });
        } catch (error: any) {
          set({
            error: error.response?.data?.error || 'Erro ao iniciar conversa',
            isLoading: false
          });
        }
      },

      // Send a message
      sendMessage: async (message: string, sessionId: string) => {
        try {
          set({ isLoading: true, error: null });
          
          // Add user message immediately
          const userMessage: Message = {
            role: 'user',
            content: message,
            timestamp: new Date().toISOString()
          };
          
          set(state => ({
            messages: [...state.messages, userMessage]
          }));

          const response = await chatApi.sendMessage(message, sessionId);
          
          // Add assistant response
          const assistantMessage: Message = {
            role: 'assistant',
            content: response.data.message,
            timestamp: response.data.timestamp
          };
          
          set(state => ({
            messages: [...state.messages, assistantMessage],
            readinessScore: response.data.analysis.readinessScore,
            isReady: response.data.analysis.isReady,
            businessData: response.data.analysis.collectedData,
            isLoading: false
          }));
        } catch (error: any) {
          set({
            error: error.response?.data?.error || 'Erro ao enviar mensagem',
            isLoading: false
          });
        }
      },

      // Load chat history
      loadChatHistory: async (sessionId: string) => {
        try {
          set({ isLoading: true, error: null });
          
          const response = await chatApi.getChatHistory(sessionId);
          
          set({
            sessionId: response.data.sessionId,
            messages: response.data.messages,
            readinessScore: response.data.analysis.readinessScore,
            isReady: response.data.analysis.isReady,
            businessData: response.data.analysis.collectedData,
            isConnected: true,
            isLoading: false
          });
        } catch (error: any) {
          set({
            error: error.response?.data?.error || 'Erro ao carregar histórico',
            isLoading: false
          });
        }
      },

      // Complete session
      completeSession: async (sessionId: string) => {
        try {
          await chatApi.completeSession(sessionId);
          
          set(state => ({
            messages: [...state.messages, {
              role: 'system',
              content: 'Conversa finalizada. Obrigado!',
              timestamp: new Date().toISOString()
            }]
          }));
        } catch (error: any) {
          set({
            error: error.response?.data?.error || 'Erro ao finalizar conversa'
          });
        }
      },

      // Clear error
      clearError: () => {
        set({ error: null });
      },

      // Reset chat
      resetChat: () => {
        set({
          sessionId: null,
          isConnected: false,
          messages: [],
          isLoading: false,
          isTyping: false,
          readinessScore: 0,
          isReady: false,
          businessData: {},
          error: null
        });
      },

      // Real-time message addition
      addMessage: (message: Message) => {
        set(state => ({
          messages: [...state.messages, message]
        }));
      },

      // Update readiness
      updateReadiness: (score: number, isReady: boolean, data?: BusinessData) => {
        set(state => ({
          readinessScore: score,
          isReady,
          businessData: data ? { ...state.businessData, ...data } : state.businessData
        }));
      }
    }),
    {
      name: 'chat-store',
      partialize: (state) => ({
        sessionId: state.sessionId,
        messages: state.messages,
        businessData: state.businessData,
        readinessScore: state.readinessScore,
        isReady: state.isReady
      })
    }
  )
);
