{"name": "infra-ai-analyst", "version": "1.0.0", "description": "Sistema de Análise de Negócios com IA - Plataforma completa para análise automatizada de processos empresariais", "main": "server/index.js", "scripts": {"dev": "concurrently \"npm run server:dev\" \"npm run client:dev\"", "server:dev": "cd server && npm run dev", "client:dev": "cd client && npm run dev", "build": "cd client && npm run build", "start": "cd server && npm start", "install:all": "npm install && cd server && npm install && cd ../client && npm install", "test": "cd server && npm test && cd ../client && npm test"}, "keywords": ["ai", "business-analysis", "automation", "n8n", "chatbot", "lead-generation", "dashboard"], "author": "<PERSON><PERSON>", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0"}}