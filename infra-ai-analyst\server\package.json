{"name": "infra-ai-analyst-server", "version": "1.0.0", "description": "Backend do Sistema de Análise de Negócios com IA", "main": "dist/index.js", "scripts": {"dev": "nodemon src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest", "test:watch": "jest --watch", "db:migrate": "npx prisma migrate dev", "db:generate": "npx prisma generate", "db:studio": "npx prisma studio"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "openai": "^4.20.1", "@google-cloud/text-to-speech": "^5.0.1", "@google-cloud/translate": "^8.0.2", "googleapis": "^128.0.0", "stripe": "^14.5.0", "axios": "^1.6.0", "joi": "^17.11.0", "rate-limiter-flexible": "^3.0.8", "winston": "^3.11.0", "prisma": "^5.6.0", "@prisma/client": "^5.6.0", "socket.io": "^4.7.4", "uuid": "^9.0.1", "moment": "^2.29.4", "csv-parser": "^3.0.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/nodemailer": "^6.4.14", "@types/node": "^20.9.0", "@types/uuid": "^9.0.7", "@types/jest": "^29.5.8", "typescript": "^5.2.2", "nodemon": "^3.0.1", "ts-node": "^10.9.1", "jest": "^29.7.0", "ts-jest": "^29.1.1", "@types/moment": "^2.13.0"}, "engines": {"node": ">=18.0.0"}}