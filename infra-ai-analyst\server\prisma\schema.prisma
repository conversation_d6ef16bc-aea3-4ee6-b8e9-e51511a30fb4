// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  name      String
  phone     String?
  company   String?
  role      String?
  industry  String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  chatSessions ChatSession[]
  analyses     Analysis[]
  payments     Payment[]

  @@map("users")
}

model ChatSession {
  id        String   @id @default(cuid())
  sessionId String   @unique
  userId    String?
  status    String   @default("active") // active, completed, abandoned
  metadata  Json?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user     User?       @relation(fields: [userId], references: [id])
  messages Message[]
  analysis Analysis?

  @@map("chat_sessions")
}

model Message {
  id            String   @id @default(cuid())
  chatSessionId String
  role          String   // user, assistant, system
  content       String
  metadata      Json?
  timestamp     DateTime @default(now())

  // Relations
  chatSession ChatSession @relation(fields: [chatSessionId], references: [id], onDelete: Cascade)

  @@map("messages")
}

model Analysis {
  id            String   @id @default(cuid())
  userId        String
  chatSessionId String   @unique
  
  // Business Information
  businessType    String
  industry        String
  companySize     String?
  currentTools    String[]
  painPoints      String[]
  goals           String[]
  
  // Generated Analysis
  blueprint       String   // Blueprint de Automação
  roadmap         String   // Roadmap para Otimizar Fluxos
  implementation  String   // Plano de Implementação
  audioUrl        String?  // URL do áudio gerado
  
  // N8N JSON
  n8nJsonUrl      String?  // URL do arquivo JSON gerado
  n8nTemplate     String?  // Template usado como base
  
  // Status
  status          String   @default("pending") // pending, completed, approved, rejected
  approvedAt      DateTime?
  
  // Metadata
  estimatedCost   Float?
  estimatedTime   String?
  complexity      String?  // low, medium, high
  priority        String?  // low, medium, high, urgent
  
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  user        User        @relation(fields: [userId], references: [id])
  chatSession ChatSession @relation(fields: [chatSessionId], references: [id])
  meetings    Meeting[]

  @@map("analyses")
}

model Meeting {
  id         String   @id @default(cuid())
  analysisId String
  
  // Meeting Details
  title       String
  description String?
  scheduledAt DateTime
  duration    Int      @default(60) // minutes
  meetingUrl  String?
  
  // Attendees
  clientEmail String
  clientName  String
  
  // Status
  status      String   @default("scheduled") // scheduled, completed, cancelled, rescheduled
  
  // Integration IDs
  googleEventId String?
  calendlyEventId String?
  
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  analysis Analysis @relation(fields: [analysisId], references: [id])

  @@map("meetings")
}

model Payment {
  id       String @id @default(cuid())
  userId   String
  
  // Payment Details
  amount          Float
  currency        String   @default("USD")
  description     String?
  
  // Payment Provider
  provider        String   // stripe, paypal
  providerPaymentId String @unique
  
  // Status
  status          String   // pending, completed, failed, refunded
  
  // Metadata
  metadata        Json?
  
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id])

  @@map("payments")
}

model AdminMetrics {
  id    String @id @default(cuid())
  date  DateTime @unique @default(now())
  
  // Daily Metrics
  totalVisits         Int @default(0)
  chatStarted         Int @default(0)
  chatCompleted       Int @default(0)
  leadsGenerated      Int @default(0)
  analysisGenerated   Int @default(0)
  meetingsScheduled   Int @default(0)
  paymentsCompleted   Int @default(0)
  
  // Revenue
  dailyRevenue        Float @default(0)
  
  // Conversion Rates (calculated)
  visitToChatRate     Float @default(0)
  chatToLeadRate      Float @default(0)
  leadToMeetingRate   Float @default(0)
  meetingToPaymentRate Float @default(0)
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("admin_metrics")
}

model SeoGlossary {
  id          String   @id @default(cuid())
  term        String   @unique
  definition  String
  category    String   // ai, automation, business, technology
  keywords    String[] // related keywords
  isActive    Boolean  @default(true)
  
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@map("seo_glossary")
}

model SystemConfig {
  id    String @id @default(cuid())
  key   String @unique
  value String
  type  String // string, number, boolean, json
  
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("system_config")
}

model AuditLog {
  id        String   @id @default(cuid())
  action    String   // user_registered, analysis_generated, payment_completed, etc.
  entityId  String?  // ID of the affected entity
  entityType String? // user, analysis, payment, etc.
  userId    String?
  metadata  Json?
  ipAddress String?
  userAgent String?
  
  createdAt DateTime @default(now())

  @@map("audit_logs")
}
