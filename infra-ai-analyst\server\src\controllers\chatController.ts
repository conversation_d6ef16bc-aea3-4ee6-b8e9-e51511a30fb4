import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { v4 as uuidv4 } from 'uuid';
import AIService, { ChatMessage, BusinessAnalysisData } from '../services/aiService';
import { asyncHandler } from '../middleware/errorHandler';
import { logger, logAudit } from '../utils/logger';

const prisma = new PrismaClient();
const aiService = AIService.getInstance();

export interface ChatRequest extends Request {
  body: {
    message: string;
    sessionId?: string;
  };
}

export interface ChatSessionRequest extends Request {
  params: {
    sessionId: string;
  };
}

// Start a new chat session
export const startChatSession = asyncHandler(async (req: Request, res: Response) => {
  const sessionId = uuidv4();
  
  // Create new chat session in database
  const chatSession = await prisma.chatSession.create({
    data: {
      sessionId,
      status: 'active',
      metadata: {
        startedAt: new Date().toISOString(),
        userAgent: req.get('User-Agent'),
        ip: req.ip
      }
    }
  });

  // Generate welcome message
  const welcomeMessage = await aiService.generateChatResponse([]);
  
  // Save welcome message
  await prisma.message.create({
    data: {
      chatSessionId: chatSession.id,
      role: 'assistant',
      content: welcomeMessage
    }
  });

  logAudit('chat_session_started', chatSession.id, 'chat_session');

  res.status(201).json({
    success: true,
    data: {
      sessionId,
      message: welcomeMessage,
      timestamp: new Date().toISOString()
    }
  });
});

// Send message to chat
export const sendMessage = asyncHandler(async (req: ChatRequest, res: Response) => {
  const { message, sessionId } = req.body;

  if (!message || !sessionId) {
    return res.status(400).json({
      success: false,
      error: 'Message and sessionId are required'
    });
  }

  // Find chat session
  const chatSession = await prisma.chatSession.findUnique({
    where: { sessionId },
    include: {
      messages: {
        orderBy: { timestamp: 'asc' }
      }
    }
  });

  if (!chatSession) {
    return res.status(404).json({
      success: false,
      error: 'Chat session not found'
    });
  }

  if (chatSession.status !== 'active') {
    return res.status(400).json({
      success: false,
      error: 'Chat session is not active'
    });
  }

  // Save user message
  await prisma.message.create({
    data: {
      chatSessionId: chatSession.id,
      role: 'user',
      content: message
    }
  });

  // Prepare conversation history
  const conversationHistory: ChatMessage[] = [
    ...chatSession.messages.map(msg => ({
      role: msg.role as 'user' | 'assistant' | 'system',
      content: msg.content,
      timestamp: msg.timestamp
    })),
    {
      role: 'user',
      content: message,
      timestamp: new Date()
    }
  ];

  // Extract current business data from conversation
  const businessData = await aiService.extractBusinessData(conversationHistory);
  
  // Update session metadata with extracted data
  await prisma.chatSession.update({
    where: { id: chatSession.id },
    data: {
      metadata: {
        ...chatSession.metadata as any,
        businessData,
        lastUpdated: new Date().toISOString()
      }
    }
  });

  // Generate AI response
  const aiResponse = await aiService.generateChatResponse(conversationHistory, businessData);

  // Save AI response
  await prisma.message.create({
    data: {
      chatSessionId: chatSession.id,
      role: 'assistant',
      content: aiResponse
    }
  });

  // Check if ready for analysis
  const readinessScore = aiService.getAnalysisReadinessScore(businessData);
  const isReady = aiService.isReadyForAnalysis(businessData);

  // Emit real-time update via Socket.IO
  const io = req.app.get('io');
  io.to(`chat-${sessionId}`).emit('message', {
    role: 'assistant',
    content: aiResponse,
    timestamp: new Date().toISOString(),
    readinessScore,
    isReady
  });

  logAudit('chat_message_sent', chatSession.id, 'chat_session', undefined, {
    messageLength: message.length,
    readinessScore,
    isReady
  });

  res.json({
    success: true,
    data: {
      message: aiResponse,
      timestamp: new Date().toISOString(),
      analysis: {
        readinessScore,
        isReady,
        collectedData: businessData
      }
    }
  });
});

// Get chat history
export const getChatHistory = asyncHandler(async (req: ChatSessionRequest, res: Response) => {
  const { sessionId } = req.params;

  const chatSession = await prisma.chatSession.findUnique({
    where: { sessionId },
    include: {
      messages: {
        orderBy: { timestamp: 'asc' }
      }
    }
  });

  if (!chatSession) {
    return res.status(404).json({
      success: false,
      error: 'Chat session not found'
    });
  }

  const businessData = (chatSession.metadata as any)?.businessData || {};
  const readinessScore = aiService.getAnalysisReadinessScore(businessData);
  const isReady = aiService.isReadyForAnalysis(businessData);

  res.json({
    success: true,
    data: {
      sessionId: chatSession.sessionId,
      status: chatSession.status,
      messages: chatSession.messages.map(msg => ({
        role: msg.role,
        content: msg.content,
        timestamp: msg.timestamp
      })),
      analysis: {
        readinessScore,
        isReady,
        collectedData: businessData
      },
      createdAt: chatSession.createdAt
    }
  });
});

// Complete chat session
export const completeChatSession = asyncHandler(async (req: ChatSessionRequest, res: Response) => {
  const { sessionId } = req.params;

  const chatSession = await prisma.chatSession.findUnique({
    where: { sessionId },
    include: {
      messages: {
        orderBy: { timestamp: 'asc' }
      }
    }
  });

  if (!chatSession) {
    return res.status(404).json({
      success: false,
      error: 'Chat session not found'
    });
  }

  // Extract final business data
  const conversationHistory: ChatMessage[] = chatSession.messages.map(msg => ({
    role: msg.role as 'user' | 'assistant' | 'system',
    content: msg.content,
    timestamp: msg.timestamp
  }));

  const businessData = await aiService.extractBusinessData(conversationHistory);
  
  // Update session status and final data
  await prisma.chatSession.update({
    where: { id: chatSession.id },
    data: {
      status: 'completed',
      metadata: {
        ...chatSession.metadata as any,
        businessData,
        completedAt: new Date().toISOString()
      }
    }
  });

  logAudit('chat_session_completed', chatSession.id, 'chat_session', undefined, {
    messageCount: chatSession.messages.length,
    businessData
  });

  res.json({
    success: true,
    data: {
      sessionId: chatSession.sessionId,
      status: 'completed',
      businessData,
      messageCount: chatSession.messages.length
    }
  });
});

// Get chat session status
export const getChatSessionStatus = asyncHandler(async (req: ChatSessionRequest, res: Response) => {
  const { sessionId } = req.params;

  const chatSession = await prisma.chatSession.findUnique({
    where: { sessionId },
    select: {
      sessionId: true,
      status: true,
      createdAt: true,
      updatedAt: true,
      metadata: true,
      _count: {
        select: {
          messages: true
        }
      }
    }
  });

  if (!chatSession) {
    return res.status(404).json({
      success: false,
      error: 'Chat session not found'
    });
  }

  const businessData = (chatSession.metadata as any)?.businessData || {};
  const readinessScore = aiService.getAnalysisReadinessScore(businessData);
  const isReady = aiService.isReadyForAnalysis(businessData);

  res.json({
    success: true,
    data: {
      sessionId: chatSession.sessionId,
      status: chatSession.status,
      messageCount: chatSession._count.messages,
      analysis: {
        readinessScore,
        isReady
      },
      createdAt: chatSession.createdAt,
      updatedAt: chatSession.updatedAt
    }
  });
});
