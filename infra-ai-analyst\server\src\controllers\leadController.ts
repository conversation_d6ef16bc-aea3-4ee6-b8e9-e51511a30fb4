import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { asyncHand<PERSON> } from '../middleware/errorHandler';
import { logger, logAudit } from '../utils/logger';
import { emailService } from '../services/emailService';
import { v4 as uuidv4 } from 'uuid';

const prisma = new PrismaClient();

export interface CaptureLeadRequest extends Request {
  body: {
    name: string;
    email: string;
    sessionId: string;
    phone?: string;
    company?: string;
  };
}

export interface GetLeadRequest extends Request {
  params: {
    leadId: string;
  };
}

// Capture a new lead
export const captureLead = asyncHandler(async (req: CaptureLeadRequest, res: Response) => {
  const { name, email, sessionId, phone, company } = req.body;

  // Validate required fields
  if (!name || !email || !sessionId) {
    return res.status(400).json({
      success: false,
      error: 'Nome, email e sessionId são obrigatórios'
    });
  }

  // Validate email format
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return res.status(400).json({
      success: false,
      error: 'Formato de email inválido'
    });
  }

  // Find the chat session
  const chatSession = await prisma.chatSession.findUnique({
    where: { sessionId },
    include: {
      messages: {
        orderBy: { timestamp: 'asc' }
      }
    }
  });

  if (!chatSession) {
    return res.status(404).json({
      success: false,
      error: 'Sessão de chat não encontrada'
    });
  }

  // Check if user already exists
  let user = await prisma.user.findUnique({
    where: { email }
  });

  // Create or update user
  if (user) {
    user = await prisma.user.update({
      where: { id: user.id },
      data: {
        name,
        phone: phone || user.phone,
        company: company || user.company,
        updatedAt: new Date()
      }
    });
  } else {
    user = await prisma.user.create({
      data: {
        id: uuidv4(),
        name,
        email,
        phone,
        company
      }
    });
  }

  // Update chat session with user
  await prisma.chatSession.update({
    where: { id: chatSession.id },
    data: {
      userId: user.id,
      metadata: {
        ...chatSession.metadata as any,
        leadCaptured: true,
        leadCapturedAt: new Date().toISOString()
      }
    }
  });

  // Extract business data from session metadata
  const businessData = (chatSession.metadata as any)?.businessData || {};

  // Prepare conversation transcript
  const conversationTranscript = chatSession.messages
    .map(msg => `${msg.role.toUpperCase()}: ${msg.content}`)
    .join('\n\n');

  // Send notification email to admin
  try {
    await emailService.sendLeadNotification({
      leadName: name,
      leadEmail: email,
      leadPhone: phone,
      leadCompany: company,
      sessionId,
      businessData,
      conversationTranscript,
      capturedAt: new Date().toISOString()
    });
  } catch (emailError) {
    logger.warn('Failed to send lead notification email', { error: emailError });
    // Don't fail the request if email fails
  }

  // Update daily metrics
  try {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    await prisma.adminMetrics.upsert({
      where: { date: today },
      update: {
        leadsGenerated: { increment: 1 }
      },
      create: {
        date: today,
        leadsGenerated: 1
      }
    });
  } catch (metricsError) {
    logger.warn('Failed to update metrics', { error: metricsError });
  }

  // Log audit event
  logAudit('lead_captured', user.id, 'user', user.id, {
    sessionId,
    email,
    name,
    company,
    phone
  });

  // Emit real-time notification to admin dashboard
  const io = req.app.get('io');
  io.emit('new_lead', {
    id: user.id,
    name,
    email,
    company,
    phone,
    sessionId,
    businessData,
    capturedAt: new Date().toISOString()
  });

  res.status(201).json({
    success: true,
    data: {
      leadId: user.id,
      message: 'Lead capturado com sucesso',
      timestamp: new Date().toISOString()
    }
  });
});

// Get lead information
export const getLead = asyncHandler(async (req: GetLeadRequest, res: Response) => {
  const { leadId } = req.params;

  const user = await prisma.user.findUnique({
    where: { id: leadId },
    include: {
      chatSessions: {
        include: {
          messages: {
            orderBy: { timestamp: 'asc' }
          },
          analysis: true
        }
      },
      analyses: {
        include: {
          meetings: true
        }
      },
      payments: true
    }
  });

  if (!user) {
    return res.status(404).json({
      success: false,
      error: 'Lead não encontrado'
    });
  }

  res.json({
    success: true,
    data: {
      id: user.id,
      name: user.name,
      email: user.email,
      phone: user.phone,
      company: user.company,
      role: user.role,
      industry: user.industry,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
      chatSessions: user.chatSessions.map(session => ({
        id: session.id,
        sessionId: session.sessionId,
        status: session.status,
        messageCount: session.messages.length,
        businessData: (session.metadata as any)?.businessData,
        createdAt: session.createdAt,
        hasAnalysis: !!session.analysis
      })),
      analyses: user.analyses.map(analysis => ({
        id: analysis.id,
        status: analysis.status,
        businessType: analysis.businessType,
        industry: analysis.industry,
        estimatedCost: analysis.estimatedCost,
        complexity: analysis.complexity,
        createdAt: analysis.createdAt,
        meetingCount: analysis.meetings.length
      })),
      paymentCount: user.payments.length,
      totalPaid: user.payments
        .filter(p => p.status === 'completed')
        .reduce((sum, p) => sum + p.amount, 0)
    }
  });
});

// Get all leads (admin only)
export const getAllLeads = asyncHandler(async (req: Request, res: Response) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 20;
  const search = req.query.search as string;
  const status = req.query.status as string;
  const sortBy = req.query.sortBy as string || 'createdAt';
  const sortOrder = req.query.sortOrder as string || 'desc';

  const skip = (page - 1) * limit;

  // Build where clause
  const where: any = {};
  
  if (search) {
    where.OR = [
      { name: { contains: search, mode: 'insensitive' } },
      { email: { contains: search, mode: 'insensitive' } },
      { company: { contains: search, mode: 'insensitive' } }
    ];
  }

  // Get leads with pagination
  const [leads, total] = await Promise.all([
    prisma.user.findMany({
      where,
      skip,
      take: limit,
      orderBy: { [sortBy]: sortOrder },
      include: {
        chatSessions: {
          select: {
            id: true,
            sessionId: true,
            status: true,
            createdAt: true,
            metadata: true
          }
        },
        analyses: {
          select: {
            id: true,
            status: true,
            businessType: true,
            industry: true,
            createdAt: true
          }
        },
        _count: {
          select: {
            chatSessions: true,
            analyses: true,
            payments: true
          }
        }
      }
    }),
    prisma.user.count({ where })
  ]);

  res.json({
    success: true,
    data: {
      leads: leads.map(lead => ({
        id: lead.id,
        name: lead.name,
        email: lead.email,
        phone: lead.phone,
        company: lead.company,
        role: lead.role,
        industry: lead.industry,
        createdAt: lead.createdAt,
        updatedAt: lead.updatedAt,
        stats: {
          chatSessions: lead._count.chatSessions,
          analyses: lead._count.analyses,
          payments: lead._count.payments
        },
        latestSession: lead.chatSessions[0] || null,
        latestAnalysis: lead.analyses[0] || null
      })),
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    }
  });
});

// Update lead information
export const updateLead = asyncHandler(async (req: Request, res: Response) => {
  const { leadId } = req.params;
  const { name, phone, company, role, industry } = req.body;

  const user = await prisma.user.findUnique({
    where: { id: leadId }
  });

  if (!user) {
    return res.status(404).json({
      success: false,
      error: 'Lead não encontrado'
    });
  }

  const updatedUser = await prisma.user.update({
    where: { id: leadId },
    data: {
      name: name || user.name,
      phone: phone || user.phone,
      company: company || user.company,
      role: role || user.role,
      industry: industry || user.industry,
      updatedAt: new Date()
    }
  });

  logAudit('lead_updated', leadId, 'user', undefined, {
    changes: { name, phone, company, role, industry }
  });

  res.json({
    success: true,
    data: updatedUser
  });
});
