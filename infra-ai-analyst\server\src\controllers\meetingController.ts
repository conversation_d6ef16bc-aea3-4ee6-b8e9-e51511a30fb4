import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { asyncHandler } from '../middleware/errorHandler';
import { logger, logAudit } from '../utils/logger';
import { calendarService } from '../services/calendarService';
import { emailService } from '../services/emailService';
import { v4 as uuidv4 } from 'uuid';

const prisma = new PrismaClient();

export interface ScheduleMeetingRequest extends Request {
  body: {
    analysisId: string;
    clientName: string;
    clientEmail: string;
    preferredDate: string;
    preferredTime: string;
    timezone: string;
    meetingType: 'consultation' | 'implementation' | 'follow-up';
    notes?: string;
  };
}

export interface GetMeetingRequest extends Request {
  params: {
    meetingId: string;
  };
}

export interface UpdateMeetingRequest extends Request {
  params: {
    meetingId: string;
  };
  body: {
    status?: 'scheduled' | 'completed' | 'cancelled' | 'rescheduled';
    scheduledAt?: string;
    notes?: string;
    meetingUrl?: string;
  };
}

// Schedule a new meeting
export const scheduleMeeting = asyncHandler(async (req: ScheduleMeetingRequest, res: Response) => {
  const {
    analysisId,
    clientName,
    clientEmail,
    preferredDate,
    preferredTime,
    timezone,
    meetingType,
    notes
  } = req.body;

  // Validate required fields
  if (!analysisId || !clientName || !clientEmail || !preferredDate || !preferredTime) {
    return res.status(400).json({
      success: false,
      error: 'Campos obrigatórios: analysisId, clientName, clientEmail, preferredDate, preferredTime'
    });
  }

  // Validate email format
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(clientEmail)) {
    return res.status(400).json({
      success: false,
      error: 'Formato de email inválido'
    });
  }

  // Find the analysis
  const analysis = await prisma.analysis.findUnique({
    where: { id: analysisId },
    include: {
      user: true,
      chatSession: true
    }
  });

  if (!analysis) {
    return res.status(404).json({
      success: false,
      error: 'Análise não encontrada'
    });
  }

  try {
    // Parse the preferred date and time
    const scheduledAt = new Date(`${preferredDate}T${preferredTime}`);
    
    // Validate that the date is in the future
    if (scheduledAt <= new Date()) {
      return res.status(400).json({
        success: false,
        error: 'Data e hora devem ser no futuro'
      });
    }

    // Determine meeting duration based on type
    const duration = getMeetingDuration(meetingType);
    
    // Generate meeting title and description
    const meetingTitle = generateMeetingTitle(meetingType, analysis.businessType);
    const meetingDescription = generateMeetingDescription(meetingType, analysis, clientName);

    // Create calendar event
    let googleEventId: string | undefined;
    let meetingUrl: string | undefined;
    
    try {
      const calendarEvent = await calendarService.createEvent({
        title: meetingTitle,
        description: meetingDescription,
        startTime: scheduledAt,
        duration,
        attendeeEmail: clientEmail,
        attendeeName: clientName,
        timezone: timezone || 'America/Sao_Paulo'
      });
      
      googleEventId = calendarEvent.id;
      meetingUrl = calendarEvent.meetingUrl;
    } catch (calendarError) {
      logger.warn('Failed to create calendar event', { error: calendarError });
      // Continue without calendar integration
    }

    // Create meeting record
    const meeting = await prisma.meeting.create({
      data: {
        id: uuidv4(),
        analysisId,
        title: meetingTitle,
        description: meetingDescription,
        scheduledAt,
        duration,
        clientEmail,
        clientName,
        meetingType,
        timezone: timezone || 'America/Sao_Paulo',
        status: 'scheduled',
        googleEventId,
        meetingUrl,
        notes
      }
    });

    // Send confirmation emails
    try {
      await emailService.sendMeetingConfirmation({
        meetingId: meeting.id,
        clientName,
        clientEmail,
        meetingTitle,
        scheduledAt,
        duration,
        meetingUrl,
        meetingType,
        businessType: analysis.businessType,
        industry: analysis.industry
      });
    } catch (emailError) {
      logger.warn('Failed to send meeting confirmation email', { error: emailError });
    }

    // Update daily metrics
    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      await prisma.adminMetrics.upsert({
        where: { date: today },
        update: {
          meetingsScheduled: { increment: 1 }
        },
        create: {
          date: today,
          meetingsScheduled: 1
        }
      });
    } catch (metricsError) {
      logger.warn('Failed to update metrics', { error: metricsError });
    }

    // Log audit event
    logAudit('meeting_scheduled', meeting.id, 'meeting', analysis.userId, {
      analysisId,
      clientEmail,
      meetingType,
      scheduledAt: scheduledAt.toISOString()
    });

    // Emit real-time notification to admin dashboard
    const io = req.app.get('io');
    io.emit('new_meeting', {
      id: meeting.id,
      clientName,
      clientEmail,
      meetingType,
      scheduledAt: scheduledAt.toISOString(),
      businessType: analysis.businessType,
      industry: analysis.industry
    });

    res.status(201).json({
      success: true,
      data: {
        meetingId: meeting.id,
        scheduledAt: meeting.scheduledAt,
        duration: meeting.duration,
        meetingUrl: meeting.meetingUrl,
        googleEventId: meeting.googleEventId,
        message: 'Reunião agendada com sucesso'
      }
    });
  } catch (error: any) {
    logger.error('Error scheduling meeting', { error, analysisId, clientEmail });
    res.status(500).json({
      success: false,
      error: 'Erro interno ao agendar reunião'
    });
  }
});

// Get meeting details
export const getMeeting = asyncHandler(async (req: GetMeetingRequest, res: Response) => {
  const { meetingId } = req.params;

  const meeting = await prisma.meeting.findUnique({
    where: { id: meetingId },
    include: {
      analysis: {
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              company: true
            }
          }
        }
      }
    }
  });

  if (!meeting) {
    return res.status(404).json({
      success: false,
      error: 'Reunião não encontrada'
    });
  }

  res.json({
    success: true,
    data: {
      id: meeting.id,
      title: meeting.title,
      description: meeting.description,
      scheduledAt: meeting.scheduledAt,
      duration: meeting.duration,
      clientName: meeting.clientName,
      clientEmail: meeting.clientEmail,
      meetingType: meeting.meetingType,
      timezone: meeting.timezone,
      status: meeting.status,
      meetingUrl: meeting.meetingUrl,
      googleEventId: meeting.googleEventId,
      notes: meeting.notes,
      analysis: meeting.analysis,
      createdAt: meeting.createdAt,
      updatedAt: meeting.updatedAt
    }
  });
});

// Get all meetings (admin)
export const getAllMeetings = asyncHandler(async (req: Request, res: Response) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 20;
  const status = req.query.status as string;
  const meetingType = req.query.meetingType as string;
  const sortBy = req.query.sortBy as string || 'scheduledAt';
  const sortOrder = req.query.sortOrder as string || 'desc';

  const skip = (page - 1) * limit;

  // Build where clause
  const where: any = {};
  
  if (status) {
    where.status = status;
  }

  if (meetingType) {
    where.meetingType = meetingType;
  }

  // Get meetings with pagination
  const [meetings, total] = await Promise.all([
    prisma.meeting.findMany({
      where,
      skip,
      take: limit,
      orderBy: { [sortBy]: sortOrder },
      include: {
        analysis: {
          select: {
            id: true,
            businessType: true,
            industry: true,
            user: {
              select: {
                name: true,
                email: true,
                company: true
              }
            }
          }
        }
      }
    }),
    prisma.meeting.count({ where })
  ]);

  res.json({
    success: true,
    data: {
      meetings: meetings.map(meeting => ({
        id: meeting.id,
        title: meeting.title,
        scheduledAt: meeting.scheduledAt,
        duration: meeting.duration,
        clientName: meeting.clientName,
        clientEmail: meeting.clientEmail,
        meetingType: meeting.meetingType,
        status: meeting.status,
        meetingUrl: meeting.meetingUrl,
        analysis: meeting.analysis,
        createdAt: meeting.createdAt
      })),
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    }
  });
});

// Helper functions
function getMeetingDuration(meetingType: string): number {
  switch (meetingType) {
    case 'consultation':
      return 60; // 1 hour
    case 'implementation':
      return 90; // 1.5 hours
    case 'follow-up':
      return 30; // 30 minutes
    default:
      return 60;
  }
}

function generateMeetingTitle(meetingType: string, businessType: string): string {
  const typeMap = {
    consultation: 'Consultoria de Automação',
    implementation: 'Reunião de Implementação',
    'follow-up': 'Follow-up de Projeto'
  };
  
  return `${typeMap[meetingType as keyof typeof typeMap] || 'Reunião'} - ${businessType}`;
}

function generateMeetingDescription(meetingType: string, analysis: any, clientName: string): string {
  const baseDescription = `Reunião agendada com ${clientName} para discutir automação empresarial.

Detalhes do Projeto:
- Tipo de Negócio: ${analysis.businessType}
- Setor: ${analysis.industry}
- Análise ID: ${analysis.id}`;

  const typeDescriptions = {
    consultation: `
Agenda da Consultoria:
1. Apresentação da análise personalizada
2. Discussão dos pontos de automação identificados
3. Esclarecimento de dúvidas
4. Definição de próximos passos
5. Proposta comercial`,
    
    implementation: `
Agenda da Implementação:
1. Revisão do plano de implementação
2. Configuração técnica inicial
3. Treinamento da equipe
4. Definição de cronograma
5. Acompanhamento e suporte`,
    
    'follow-up': `
Agenda do Follow-up:
1. Revisão do progresso atual
2. Identificação de desafios
3. Ajustes necessários
4. Próximas etapas
5. Feedback e melhorias`
  };

  return baseDescription + (typeDescriptions[meetingType as keyof typeof typeDescriptions] || '');
}
