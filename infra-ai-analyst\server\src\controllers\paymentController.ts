import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { asyncHandler } from '../middleware/errorHandler';
import { logger, logAudit } from '../utils/logger';
import { stripeService } from '../services/stripeService';
import { emailService } from '../services/emailService';
import { v4 as uuidv4 } from 'uuid';

const prisma = new PrismaClient();

export interface CreatePaymentRequest extends Request {
  body: {
    analysisId: string;
    amount: number;
    currency: string;
    description: string;
    customerEmail: string;
    customerName: string;
    paymentMethod?: 'stripe' | 'pix' | 'bank_transfer';
    metadata?: any;
  };
}

export interface GetPaymentRequest extends Request {
  params: {
    paymentId: string;
  };
}

export interface WebhookRequest extends Request {
  body: any;
  headers: {
    'stripe-signature'?: string;
  };
}

// Create payment intent
export const createPayment = asyncHandler(async (req: CreatePaymentRequest, res: Response) => {
  const {
    analysisId,
    amount,
    currency = 'usd',
    description,
    customerEmail,
    customerName,
    paymentMethod = 'stripe',
    metadata
  } = req.body;

  // Validate required fields
  if (!analysisId || !amount || !customerEmail || !customerName) {
    return res.status(400).json({
      success: false,
      error: 'Campos obrigatórios: analysisId, amount, customerEmail, customerName'
    });
  }

  // Validate amount
  if (amount <= 0) {
    return res.status(400).json({
      success: false,
      error: 'Valor deve ser maior que zero'
    });
  }

  // Find the analysis
  const analysis = await prisma.analysis.findUnique({
    where: { id: analysisId },
    include: {
      user: true
    }
  });

  if (!analysis) {
    return res.status(404).json({
      success: false,
      error: 'Análise não encontrada'
    });
  }

  try {
    // Create payment record
    const payment = await prisma.payment.create({
      data: {
        id: uuidv4(),
        userId: analysis.userId,
        analysisId,
        amount,
        currency: currency.toUpperCase(),
        description,
        customerEmail,
        customerName,
        paymentMethod,
        status: 'pending',
        metadata: metadata || {}
      }
    });

    let paymentIntent: any = null;
    let clientSecret: string | undefined;

    // Create payment intent based on method
    if (paymentMethod === 'stripe') {
      try {
        paymentIntent = await stripeService.createPaymentIntent({
          amount: Math.round(amount * 100), // Convert to cents
          currency: currency.toLowerCase(),
          customerEmail,
          customerName,
          description,
          metadata: {
            paymentId: payment.id,
            analysisId,
            userId: analysis.userId
          }
        });

        clientSecret = paymentIntent.client_secret;

        // Update payment with Stripe data
        await prisma.payment.update({
          where: { id: payment.id },
          data: {
            stripePaymentIntentId: paymentIntent.id,
            metadata: {
              ...payment.metadata as any,
              stripePaymentIntentId: paymentIntent.id
            }
          }
        });
      } catch (stripeError) {
        logger.error('Stripe payment intent creation failed', { error: stripeError });
        // Continue with manual payment flow
      }
    }

    // Log audit event
    logAudit('payment_created', payment.id, 'payment', analysis.userId, {
      amount,
      currency,
      paymentMethod,
      analysisId
    });

    res.status(201).json({
      success: true,
      data: {
        paymentId: payment.id,
        amount: payment.amount,
        currency: payment.currency,
        status: payment.status,
        paymentMethod: payment.paymentMethod,
        clientSecret,
        stripePaymentIntentId: paymentIntent?.id,
        instructions: getPaymentInstructions(paymentMethod, amount, currency)
      }
    });
  } catch (error: any) {
    logger.error('Error creating payment', { error, analysisId });
    res.status(500).json({
      success: false,
      error: 'Erro interno ao criar pagamento'
    });
  }
});

// Get payment details
export const getPayment = asyncHandler(async (req: GetPaymentRequest, res: Response) => {
  const { paymentId } = req.params;

  const payment = await prisma.payment.findUnique({
    where: { id: paymentId },
    include: {
      user: {
        select: {
          id: true,
          name: true,
          email: true,
          company: true
        }
      },
      analysis: {
        select: {
          id: true,
          businessType: true,
          industry: true
        }
      }
    }
  });

  if (!payment) {
    return res.status(404).json({
      success: false,
      error: 'Pagamento não encontrado'
    });
  }

  res.json({
    success: true,
    data: {
      id: payment.id,
      amount: payment.amount,
      currency: payment.currency,
      description: payment.description,
      customerName: payment.customerName,
      customerEmail: payment.customerEmail,
      paymentMethod: payment.paymentMethod,
      status: payment.status,
      stripePaymentIntentId: payment.stripePaymentIntentId,
      metadata: payment.metadata,
      user: payment.user,
      analysis: payment.analysis,
      createdAt: payment.createdAt,
      updatedAt: payment.updatedAt,
      completedAt: payment.completedAt
    }
  });
});

// Get all payments (admin)
export const getAllPayments = asyncHandler(async (req: Request, res: Response) => {
  const page = parseInt(req.query.page as string) || 1;
  const limit = parseInt(req.query.limit as string) || 20;
  const status = req.query.status as string;
  const paymentMethod = req.query.paymentMethod as string;
  const sortBy = req.query.sortBy as string || 'createdAt';
  const sortOrder = req.query.sortOrder as string || 'desc';

  const skip = (page - 1) * limit;

  // Build where clause
  const where: any = {};
  
  if (status) {
    where.status = status;
  }

  if (paymentMethod) {
    where.paymentMethod = paymentMethod;
  }

  // Get payments with pagination
  const [payments, total] = await Promise.all([
    prisma.payment.findMany({
      where,
      skip,
      take: limit,
      orderBy: { [sortBy]: sortOrder },
      include: {
        user: {
          select: {
            name: true,
            email: true,
            company: true
          }
        },
        analysis: {
          select: {
            businessType: true,
            industry: true
          }
        }
      }
    }),
    prisma.payment.count({ where })
  ]);

  res.json({
    success: true,
    data: {
      payments: payments.map(payment => ({
        id: payment.id,
        amount: payment.amount,
        currency: payment.currency,
        description: payment.description,
        customerName: payment.customerName,
        customerEmail: payment.customerEmail,
        paymentMethod: payment.paymentMethod,
        status: payment.status,
        user: payment.user,
        analysis: payment.analysis,
        createdAt: payment.createdAt,
        completedAt: payment.completedAt
      })),
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    }
  });
});

// Stripe webhook handler
export const handleStripeWebhook = asyncHandler(async (req: WebhookRequest, res: Response) => {
  const sig = req.headers['stripe-signature'];
  
  if (!sig) {
    return res.status(400).json({ error: 'Missing stripe signature' });
  }

  try {
    const event = stripeService.constructWebhookEvent(req.body, sig);

    switch (event.type) {
      case 'payment_intent.succeeded':
        await handlePaymentSuccess(event.data.object);
        break;
      case 'payment_intent.payment_failed':
        await handlePaymentFailure(event.data.object);
        break;
      default:
        logger.info('Unhandled Stripe event type', { type: event.type });
    }

    res.json({ received: true });
  } catch (error: any) {
    logger.error('Stripe webhook error', { error });
    res.status(400).json({ error: 'Webhook signature verification failed' });
  }
});

// Handle successful payment
async function handlePaymentSuccess(paymentIntent: any) {
  try {
    const payment = await prisma.payment.findFirst({
      where: { stripePaymentIntentId: paymentIntent.id },
      include: {
        user: true,
        analysis: true
      }
    });

    if (!payment) {
      logger.warn('Payment not found for successful payment intent', { 
        paymentIntentId: paymentIntent.id 
      });
      return;
    }

    // Update payment status
    await prisma.payment.update({
      where: { id: payment.id },
      data: {
        status: 'completed',
        completedAt: new Date(),
        metadata: {
          ...payment.metadata as any,
          stripeChargeId: paymentIntent.latest_charge
        }
      }
    });

    // Update daily metrics
    try {
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      await prisma.adminMetrics.upsert({
        where: { date: today },
        update: {
          paymentsCompleted: { increment: 1 },
          dailyRevenue: { increment: payment.amount }
        },
        create: {
          date: today,
          paymentsCompleted: 1,
          dailyRevenue: payment.amount
        }
      });
    } catch (metricsError) {
      logger.warn('Failed to update metrics', { error: metricsError });
    }

    // Send confirmation emails
    try {
      await emailService.sendPaymentConfirmation({
        paymentId: payment.id,
        customerName: payment.customerName,
        customerEmail: payment.customerEmail,
        amount: payment.amount,
        currency: payment.currency,
        description: payment.description,
        businessType: payment.analysis?.businessType,
        industry: payment.analysis?.industry
      });
    } catch (emailError) {
      logger.warn('Failed to send payment confirmation email', { error: emailError });
    }

    // Log audit event
    logAudit('payment_completed', payment.id, 'payment', payment.userId, {
      amount: payment.amount,
      currency: payment.currency,
      stripeChargeId: paymentIntent.latest_charge
    });

    logger.info('Payment completed successfully', { 
      paymentId: payment.id,
      amount: payment.amount 
    });
  } catch (error) {
    logger.error('Error handling payment success', { error, paymentIntentId: paymentIntent.id });
  }
}

// Handle failed payment
async function handlePaymentFailure(paymentIntent: any) {
  try {
    const payment = await prisma.payment.findFirst({
      where: { stripePaymentIntentId: paymentIntent.id }
    });

    if (!payment) {
      logger.warn('Payment not found for failed payment intent', { 
        paymentIntentId: paymentIntent.id 
      });
      return;
    }

    // Update payment status
    await prisma.payment.update({
      where: { id: payment.id },
      data: {
        status: 'failed',
        metadata: {
          ...payment.metadata as any,
          failureReason: paymentIntent.last_payment_error?.message
        }
      }
    });

    // Log audit event
    logAudit('payment_failed', payment.id, 'payment', payment.userId, {
      failureReason: paymentIntent.last_payment_error?.message
    });

    logger.info('Payment failed', { 
      paymentId: payment.id,
      reason: paymentIntent.last_payment_error?.message 
    });
  } catch (error) {
    logger.error('Error handling payment failure', { error, paymentIntentId: paymentIntent.id });
  }
}

// Helper function to get payment instructions
function getPaymentInstructions(method: string, amount: number, currency: string): any {
  const formattedAmount = new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: currency.toUpperCase()
  }).format(amount);

  switch (method) {
    case 'stripe':
      return {
        type: 'card',
        message: 'Complete o pagamento usando seu cartão de crédito/débito',
        amount: formattedAmount
      };
    case 'pix':
      return {
        type: 'pix',
        message: 'Escaneie o código QR ou copie a chave PIX para completar o pagamento',
        amount: formattedAmount,
        pixKey: process.env.PIX_KEY || '<EMAIL>'
      };
    case 'bank_transfer':
      return {
        type: 'bank_transfer',
        message: 'Realize a transferência bancária usando os dados abaixo',
        amount: formattedAmount,
        bankDetails: {
          bank: 'Banco do Brasil',
          agency: '1234-5',
          account: '12345-6',
          accountHolder: 'Infra AI Analyst LTDA',
          cnpj: '12.345.678/0001-90'
        }
      };
    default:
      return {
        type: 'manual',
        message: 'Entre em contato para finalizar o pagamento',
        amount: formattedAmount
      };
  }
}
