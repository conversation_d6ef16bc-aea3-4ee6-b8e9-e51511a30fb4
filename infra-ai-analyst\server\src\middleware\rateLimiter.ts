import { Request, Response, NextFunction } from 'express';
import { RateLimiterMemory, RateLimiterRedis } from 'rate-limiter-flexible';
import { logger } from '../utils/logger';
import { RateLimitError } from './errorHandler';

// Redis connection (optional, falls back to memory)
let redisClient: any = null;
try {
  if (process.env.REDIS_URL) {
    const redis = require('redis');
    redisClient = redis.createClient({
      url: process.env.REDIS_URL,
      password: process.env.REDIS_PASSWORD
    });
    redisClient.connect();
  }
} catch (error) {
  logger.warn('Redis not available, using memory rate limiter', { error: error.message });
}

// Rate limiter configurations
const rateLimiterConfig = {
  keyPrefix: 'rl_',
  points: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'), // Number of requests
  duration: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000') / 1000, // Per 15 minutes (in seconds)
  blockDuration: 60, // Block for 1 minute if limit exceeded
};

// Create rate limiter instance
const rateLimiter = redisClient 
  ? new RateLimiterRedis({
      storeClient: redisClient,
      ...rateLimiterConfig
    })
  : new RateLimiterMemory(rateLimiterConfig);

// Specific rate limiters for different endpoints
const chatRateLimiter = redisClient
  ? new RateLimiterRedis({
      storeClient: redisClient,
      keyPrefix: 'rl_chat_',
      points: 50, // 50 chat messages
      duration: 3600, // per hour
      blockDuration: 300, // block for 5 minutes
    })
  : new RateLimiterMemory({
      keyPrefix: 'rl_chat_',
      points: 50,
      duration: 3600,
      blockDuration: 300,
    });

const analysisRateLimiter = redisClient
  ? new RateLimiterRedis({
      storeClient: redisClient,
      keyPrefix: 'rl_analysis_',
      points: 5, // 5 analysis requests
      duration: 3600, // per hour
      blockDuration: 1800, // block for 30 minutes
    })
  : new RateLimiterMemory({
      keyPrefix: 'rl_analysis_',
      points: 5,
      duration: 3600,
      blockDuration: 1800,
    });

const leadRateLimiter = redisClient
  ? new RateLimiterRedis({
      storeClient: redisClient,
      keyPrefix: 'rl_lead_',
      points: 3, // 3 lead submissions
      duration: 3600, // per hour
      blockDuration: 3600, // block for 1 hour
    })
  : new RateLimiterMemory({
      keyPrefix: 'rl_lead_',
      points: 3,
      duration: 3600,
      blockDuration: 3600,
    });

// Helper function to get client identifier
const getClientId = (req: Request): string => {
  // Try to get user ID first, then fall back to IP
  const userId = (req as any).user?.id;
  if (userId) return `user_${userId}`;
  
  // Get IP address (considering proxies)
  const forwarded = req.headers['x-forwarded-for'] as string;
  const ip = forwarded ? forwarded.split(',')[0] : req.connection.remoteAddress;
  return `ip_${ip}`;
};

// Generic rate limiter middleware factory
const createRateLimiterMiddleware = (limiter: any, errorMessage?: string) => {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const clientId = getClientId(req);
      await limiter.consume(clientId);
      next();
    } catch (rejRes: any) {
      const secs = Math.round(rejRes.msBeforeNext / 1000) || 1;
      
      // Log rate limit hit
      logger.warn('Rate limit exceeded', {
        clientId: getClientId(req),
        path: req.path,
        method: req.method,
        remainingPoints: rejRes.remainingPoints,
        msBeforeNext: rejRes.msBeforeNext,
        totalHits: rejRes.totalHits
      });

      // Set rate limit headers
      res.set('Retry-After', String(secs));
      res.set('X-RateLimit-Limit', String(limiter.points));
      res.set('X-RateLimit-Remaining', String(rejRes.remainingPoints || 0));
      res.set('X-RateLimit-Reset', String(new Date(Date.now() + rejRes.msBeforeNext)));

      throw new RateLimitError(
        errorMessage || `Too many requests. Try again in ${secs} seconds.`
      );
    }
  };
};

// Export middleware functions
export const rateLimiter = createRateLimiterMiddleware(rateLimiter);

export const chatRateLimit = createRateLimiterMiddleware(
  chatRateLimiter,
  'Too many chat messages. Please wait before sending more messages.'
);

export const analysisRateLimit = createRateLimiterMiddleware(
  analysisRateLimiter,
  'Too many analysis requests. Please wait before requesting another analysis.'
);

export const leadRateLimit = createRateLimiterMiddleware(
  leadRateLimiter,
  'Too many lead submissions. Please wait before submitting again.'
);

// Middleware to add rate limit headers to successful requests
export const addRateLimitHeaders = (limiterType: 'general' | 'chat' | 'analysis' | 'lead' = 'general') => {
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      let limiter;
      switch (limiterType) {
        case 'chat':
          limiter = chatRateLimiter;
          break;
        case 'analysis':
          limiter = analysisRateLimiter;
          break;
        case 'lead':
          limiter = leadRateLimiter;
          break;
        default:
          limiter = rateLimiter;
      }

      const clientId = getClientId(req);
      const resRateLimiter = await limiter.get(clientId);
      
      if (resRateLimiter) {
        res.set('X-RateLimit-Limit', String(limiter.points));
        res.set('X-RateLimit-Remaining', String(resRateLimiter.remainingPoints));
        res.set('X-RateLimit-Reset', String(new Date(Date.now() + resRateLimiter.msBeforeNext)));
      }
      
      next();
    } catch (error) {
      // If there's an error getting rate limit info, just continue
      next();
    }
  };
};

export default rateLimiter;
