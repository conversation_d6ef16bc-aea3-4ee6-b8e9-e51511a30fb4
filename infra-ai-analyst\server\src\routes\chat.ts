import { Router } from 'express';
import {
  startChatSession,
  sendMessage,
  getChatHistory,
  completeChatSession,
  getChatSessionStatus
} from '../controllers/chatController';
import { chatRateLimit, addRateLimitHeaders } from '../middleware/rateLimiter';
import { body, param, validationResult } from 'express-validator';
import { Request, Response, NextFunction } from 'express';

const router = Router();

// Validation middleware
const validateRequest = (req: Request, res: Response, next: NextFunction) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array()
    });
  }
  next();
};

// Validation rules
const sessionIdValidation = param('sessionId')
  .isUUID()
  .withMessage('Session ID must be a valid UUID');

const messageValidation = [
  body('message')
    .isString()
    .trim()
    .isLength({ min: 1, max: 2000 })
    .withMessage('Message must be between 1 and 2000 characters'),
  body('sessionId')
    .optional()
    .isUUID()
    .withMessage('Session ID must be a valid UUID')
];

/**
 * @route   POST /api/chat/start
 * @desc    Start a new chat session
 * @access  Public
 */
router.post('/start', 
  addRateLimitHeaders('chat'),
  startChatSession
);

/**
 * @route   POST /api/chat/message
 * @desc    Send a message to the chat
 * @access  Public
 */
router.post('/message',
  chatRateLimit,
  addRateLimitHeaders('chat'),
  messageValidation,
  validateRequest,
  sendMessage
);

/**
 * @route   GET /api/chat/:sessionId/history
 * @desc    Get chat history for a session
 * @access  Public
 */
router.get('/:sessionId/history',
  sessionIdValidation,
  validateRequest,
  getChatHistory
);

/**
 * @route   POST /api/chat/:sessionId/complete
 * @desc    Mark chat session as completed
 * @access  Public
 */
router.post('/:sessionId/complete',
  sessionIdValidation,
  validateRequest,
  completeChatSession
);

/**
 * @route   GET /api/chat/:sessionId/status
 * @desc    Get chat session status and readiness
 * @access  Public
 */
router.get('/:sessionId/status',
  sessionIdValidation,
  validateRequest,
  getChatSessionStatus
);

/**
 * @route   GET /api/chat/health
 * @desc    Health check for chat service
 * @access  Public
 */
router.get('/health', (req: Request, res: Response) => {
  res.json({
    success: true,
    service: 'chat',
    status: 'healthy',
    timestamp: new Date().toISOString(),
    features: {
      openai: !!process.env.OPENAI_API_KEY,
      gemini: !!process.env.GOOGLE_AI_API_KEY,
      realtime: true
    }
  });
});

export default router;
