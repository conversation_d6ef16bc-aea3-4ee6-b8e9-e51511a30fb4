import { Router } from 'express';
import {
  captureLead,
  getLead,
  getAllLeads,
  updateLead
} from '../controllers/leadController';
import { leadRateLimit, addRateLimitHeaders } from '../middleware/rateLimiter';
import { body, param, query, validationResult } from 'express-validator';
import { Request, Response, NextFunction } from 'express';

const router = Router();

// Validation middleware
const validateRequest = (req: Request, res: Response, next: NextFunction) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array()
    });
  }
  next();
};

// Validation rules
const captureLeadValidation = [
  body('name')
    .isString()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Nome deve ter entre 2 e 100 caracteres'),
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Email deve ter um formato válido'),
  body('sessionId')
    .isUUID()
    .withMessage('Session ID deve ser um UUID válido'),
  body('phone')
    .optional()
    .isMobilePhone('pt-BR')
    .withMessage('Telefone deve ter um formato válido'),
  body('company')
    .optional()
    .isString()
    .trim()
    .isLength({ max: 200 })
    .withMessage('Nome da empresa deve ter no máximo 200 caracteres')
];

const leadIdValidation = param('leadId')
  .isUUID()
  .withMessage('Lead ID deve ser um UUID válido');

const updateLeadValidation = [
  leadIdValidation,
  body('name')
    .optional()
    .isString()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Nome deve ter entre 2 e 100 caracteres'),
  body('phone')
    .optional()
    .isMobilePhone('pt-BR')
    .withMessage('Telefone deve ter um formato válido'),
  body('company')
    .optional()
    .isString()
    .trim()
    .isLength({ max: 200 })
    .withMessage('Nome da empresa deve ter no máximo 200 caracteres'),
  body('role')
    .optional()
    .isString()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Cargo deve ter no máximo 100 caracteres'),
  body('industry')
    .optional()
    .isString()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Setor deve ter no máximo 100 caracteres')
];

const getAllLeadsValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Página deve ser um número inteiro maior que 0'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limite deve ser um número entre 1 e 100'),
  query('search')
    .optional()
    .isString()
    .trim()
    .isLength({ max: 100 })
    .withMessage('Busca deve ter no máximo 100 caracteres'),
  query('sortBy')
    .optional()
    .isIn(['createdAt', 'updatedAt', 'name', 'email', 'company'])
    .withMessage('Campo de ordenação inválido'),
  query('sortOrder')
    .optional()
    .isIn(['asc', 'desc'])
    .withMessage('Ordem deve ser asc ou desc')
];

/**
 * @route   POST /api/leads/capture
 * @desc    Capture a new lead (critical flow)
 * @access  Public
 */
router.post('/capture',
  leadRateLimit,
  addRateLimitHeaders('lead'),
  captureLeadValidation,
  validateRequest,
  captureLead
);

/**
 * @route   GET /api/leads/:leadId
 * @desc    Get lead information
 * @access  Public (with lead ID)
 */
router.get('/:leadId',
  leadIdValidation,
  validateRequest,
  getLead
);

/**
 * @route   GET /api/leads
 * @desc    Get all leads (admin only)
 * @access  Admin
 */
router.get('/',
  getAllLeadsValidation,
  validateRequest,
  getAllLeads
);

/**
 * @route   PUT /api/leads/:leadId
 * @desc    Update lead information
 * @access  Admin
 */
router.put('/:leadId',
  updateLeadValidation,
  validateRequest,
  updateLead
);

/**
 * @route   GET /api/leads/health
 * @desc    Health check for leads service
 * @access  Public
 */
router.get('/health', (req: Request, res: Response) => {
  res.json({
    success: true,
    service: 'leads',
    status: 'healthy',
    timestamp: new Date().toISOString(),
    features: {
      emailNotifications: !!process.env.SMTP_HOST,
      realTimeUpdates: true,
      rateLimiting: true
    }
  });
});

export default router;
