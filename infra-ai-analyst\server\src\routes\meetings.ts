import { Router } from 'express';
import {
  scheduleMeeting,
  getMeeting,
  getAllMeetings
} from '../controllers/meetingController';
import { meetingRateLimit, addRateLimitHeaders } from '../middleware/rateLimiter';
import { body, param, query, validationResult } from 'express-validator';
import { Request, Response, NextFunction } from 'express';

const router = Router();

// Validation middleware
const validateRequest = (req: Request, res: Response, next: NextFunction) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array()
    });
  }
  next();
};

// Validation rules
const scheduleMeetingValidation = [
  body('analysisId')
    .isUUID()
    .withMessage('Analysis ID deve ser um UUID válido'),
  body('clientName')
    .isString()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Nome do cliente deve ter entre 2 e 100 caracteres'),
  body('clientEmail')
    .isEmail()
    .normalizeEmail()
    .withMessage('Email deve ter um formato válido'),
  body('preferredDate')
    .isISO8601()
    .withMessage('Data preferida deve estar no formato ISO8601 (YYYY-MM-DD)'),
  body('preferredTime')
    .matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/)
    .withMessage('Horário preferido deve estar no formato HH:MM'),
  body('timezone')
    .optional()
    .isString()
    .withMessage('Timezone deve ser uma string válida'),
  body('meetingType')
    .isIn(['consultation', 'implementation', 'follow-up'])
    .withMessage('Tipo de reunião deve ser consultation, implementation ou follow-up'),
  body('notes')
    .optional()
    .isString()
    .trim()
    .isLength({ max: 500 })
    .withMessage('Notas devem ter no máximo 500 caracteres')
];

const meetingIdValidation = param('meetingId')
  .isUUID()
  .withMessage('Meeting ID deve ser um UUID válido');

const getAllMeetingsValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Página deve ser um número inteiro maior que 0'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limite deve ser um número entre 1 e 100'),
  query('status')
    .optional()
    .isIn(['scheduled', 'completed', 'cancelled', 'rescheduled'])
    .withMessage('Status deve ser scheduled, completed, cancelled ou rescheduled'),
  query('meetingType')
    .optional()
    .isIn(['consultation', 'implementation', 'follow-up'])
    .withMessage('Tipo de reunião deve ser consultation, implementation ou follow-up'),
  query('sortBy')
    .optional()
    .isIn(['scheduledAt', 'createdAt', 'clientName', 'meetingType'])
    .withMessage('Campo de ordenação inválido'),
  query('sortOrder')
    .optional()
    .isIn(['asc', 'desc'])
    .withMessage('Ordem deve ser asc ou desc')
];

/**
 * @route   POST /api/meetings/schedule
 * @desc    Schedule a new meeting (critical flow)
 * @access  Public
 */
router.post('/schedule',
  meetingRateLimit,
  addRateLimitHeaders('meeting'),
  scheduleMeetingValidation,
  validateRequest,
  scheduleMeeting
);

/**
 * @route   GET /api/meetings/:meetingId
 * @desc    Get meeting details
 * @access  Public (with meeting ID)
 */
router.get('/:meetingId',
  meetingIdValidation,
  validateRequest,
  getMeeting
);

/**
 * @route   GET /api/meetings
 * @desc    Get all meetings (admin only)
 * @access  Admin
 */
router.get('/',
  getAllMeetingsValidation,
  validateRequest,
  getAllMeetings
);

/**
 * @route   GET /api/meetings/health
 * @desc    Health check for meetings service
 * @access  Public
 */
router.get('/health', (req: Request, res: Response) => {
  res.json({
    success: true,
    service: 'meetings',
    status: 'healthy',
    timestamp: new Date().toISOString(),
    features: {
      calendarIntegration: !!process.env.GOOGLE_APPLICATION_CREDENTIALS,
      emailNotifications: !!process.env.SMTP_HOST,
      rateLimiting: true,
      timeZoneSupport: true
    }
  });
});

export default router;
