import { Router } from 'express';
import {
  createPayment,
  getPayment,
  getAllPayments,
  handleStripeWebhook
} from '../controllers/paymentController';
import { paymentRateLimit, addRateLimitHeaders } from '../middleware/rateLimiter';
import { body, param, query, validationResult } from 'express-validator';
import { Request, Response, NextFunction } from 'express';
import express from 'express';

const router = Router();

// Validation middleware
const validateRequest = (req: Request, res: Response, next: NextFunction) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array()
    });
  }
  next();
};

// Validation rules
const createPaymentValidation = [
  body('analysisId')
    .isUUID()
    .withMessage('Analysis ID deve ser um UUID válido'),
  body('amount')
    .isFloat({ min: 0.01 })
    .withMessage('Valor deve ser maior que zero'),
  body('currency')
    .optional()
    .isIn(['USD', 'BRL', 'EUR'])
    .withMessage('Moeda deve ser USD, BRL ou EUR'),
  body('description')
    .isString()
    .trim()
    .isLength({ min: 5, max: 200 })
    .withMessage('Descrição deve ter entre 5 e 200 caracteres'),
  body('customerEmail')
    .isEmail()
    .normalizeEmail()
    .withMessage('Email deve ter um formato válido'),
  body('customerName')
    .isString()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage('Nome do cliente deve ter entre 2 e 100 caracteres'),
  body('paymentMethod')
    .optional()
    .isIn(['stripe', 'pix', 'bank_transfer'])
    .withMessage('Método de pagamento deve ser stripe, pix ou bank_transfer'),
  body('metadata')
    .optional()
    .isObject()
    .withMessage('Metadata deve ser um objeto válido')
];

const paymentIdValidation = param('paymentId')
  .isUUID()
  .withMessage('Payment ID deve ser um UUID válido');

const getAllPaymentsValidation = [
  query('page')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Página deve ser um número inteiro maior que 0'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limite deve ser um número entre 1 e 100'),
  query('status')
    .optional()
    .isIn(['pending', 'completed', 'failed', 'cancelled', 'refunded'])
    .withMessage('Status deve ser pending, completed, failed, cancelled ou refunded'),
  query('paymentMethod')
    .optional()
    .isIn(['stripe', 'pix', 'bank_transfer'])
    .withMessage('Método de pagamento deve ser stripe, pix ou bank_transfer'),
  query('sortBy')
    .optional()
    .isIn(['createdAt', 'amount', 'customerName', 'status'])
    .withMessage('Campo de ordenação inválido'),
  query('sortOrder')
    .optional()
    .isIn(['asc', 'desc'])
    .withMessage('Ordem deve ser asc ou desc')
];

/**
 * @route   POST /api/payments/create
 * @desc    Create a new payment (critical flow)
 * @access  Public
 */
router.post('/create',
  paymentRateLimit,
  addRateLimitHeaders('payment'),
  createPaymentValidation,
  validateRequest,
  createPayment
);

/**
 * @route   GET /api/payments/:paymentId
 * @desc    Get payment details
 * @access  Public (with payment ID)
 */
router.get('/:paymentId',
  paymentIdValidation,
  validateRequest,
  getPayment
);

/**
 * @route   GET /api/payments
 * @desc    Get all payments (admin only)
 * @access  Admin
 */
router.get('/',
  getAllPaymentsValidation,
  validateRequest,
  getAllPayments
);

/**
 * @route   POST /api/payments/webhook/stripe
 * @desc    Handle Stripe webhooks
 * @access  Stripe
 */
router.post('/webhook/stripe',
  express.raw({ type: 'application/json' }),
  handleStripeWebhook
);

/**
 * @route   GET /api/payments/config/stripe
 * @desc    Get Stripe configuration
 * @access  Public
 */
router.get('/config/stripe', (req: Request, res: Response) => {
  res.json({
    success: true,
    data: {
      publishableKey: process.env.STRIPE_PUBLISHABLE_KEY,
      currency: process.env.DEFAULT_CURRENCY || 'usd',
      country: process.env.STRIPE_COUNTRY || 'US'
    }
  });
});

/**
 * @route   GET /api/payments/methods/available
 * @desc    Get available payment methods
 * @access  Public
 */
router.get('/methods/available', (req: Request, res: Response) => {
  const availableMethods = [];

  // Check Stripe availability
  if (process.env.STRIPE_SECRET_KEY && process.env.STRIPE_PUBLISHABLE_KEY) {
    availableMethods.push({
      id: 'stripe',
      name: 'Cartão de Crédito/Débito',
      description: 'Pagamento seguro com cartão via Stripe',
      icon: 'credit-card',
      currencies: ['USD', 'BRL', 'EUR'],
      processingTime: 'Imediato'
    });
  }

  // PIX (Brazil)
  if (process.env.PIX_KEY) {
    availableMethods.push({
      id: 'pix',
      name: 'PIX',
      description: 'Transferência instantânea via PIX',
      icon: 'pix',
      currencies: ['BRL'],
      processingTime: 'Até 2 horas'
    });
  }

  // Bank Transfer
  availableMethods.push({
    id: 'bank_transfer',
    name: 'Transferência Bancária',
    description: 'Transferência bancária tradicional',
    icon: 'bank',
    currencies: ['USD', 'BRL', 'EUR'],
    processingTime: '1-3 dias úteis'
  });

  res.json({
    success: true,
    data: {
      methods: availableMethods,
      defaultCurrency: process.env.DEFAULT_CURRENCY || 'usd'
    }
  });
});

/**
 * @route   GET /api/payments/health
 * @desc    Health check for payments service
 * @access  Public
 */
router.get('/health', (req: Request, res: Response) => {
  res.json({
    success: true,
    service: 'payments',
    status: 'healthy',
    timestamp: new Date().toISOString(),
    features: {
      stripeIntegration: !!(process.env.STRIPE_SECRET_KEY && process.env.STRIPE_PUBLISHABLE_KEY),
      pixPayments: !!process.env.PIX_KEY,
      bankTransfer: true,
      webhooks: !!process.env.STRIPE_WEBHOOK_SECRET,
      emailNotifications: !!process.env.SMTP_HOST,
      rateLimiting: true
    }
  });
});

export default router;
