import OpenAI from 'openai';
import { GoogleGenerativeAI } from '@google/generative-ai';
import { logger, logError } from '../utils/logger';

// Initialize AI clients
const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

const genAI = process.env.GOOGLE_AI_API_KEY 
  ? new GoogleGenerativeAI(process.env.GOOGLE_AI_API_KEY)
  : null;

export interface ChatMessage {
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp?: Date;
}

export interface BusinessAnalysisData {
  businessType: string;
  industry: string;
  companySize?: string;
  currentTools: string[];
  painPoints: string[];
  goals: string[];
  budget?: string;
  timeline?: string;
  technicalLevel?: string;
}

export class AIService {
  private static instance: AIService;
  
  public static getInstance(): AIService {
    if (!AIService.instance) {
      AIService.instance = new AIService();
    }
    return AIService.instance;
  }

  // Business Analysis Chatbot System Prompt
  private getBusinessAnalysisPrompt(): string {
    return `Você é um consultor de negócios especializado em automação e inteligência artificial. Sua missão é mapear completamente o negócio do usuário através de uma conversa natural e estruturada.

OBJETIVO: Coletar informações detalhadas sobre:
1. Tipo de negócio e indústria
2. Tamanho da empresa e estrutura
3. Processos atuais e ferramentas utilizadas
4. Principais desafios e pontos de dor
5. Objetivos e metas de crescimento
6. Orçamento e cronograma disponível
7. Nível técnico da equipe

ESTILO DE CONVERSA:
- Seja amigável, profissional e consultivo
- Faça uma pergunta por vez para não sobrecarregar
- Use linguagem clara e acessível
- Demonstre expertise sem ser técnico demais
- Seja empático aos desafios mencionados
- Mantenha o foco nos resultados de negócio

FLUXO DE PERGUNTAS:
1. Cumprimento e apresentação
2. Identificação do tipo de negócio
3. Tamanho e estrutura da empresa
4. Processos e ferramentas atuais
5. Principais desafios
6. Objetivos e metas
7. Recursos disponíveis
8. Finalização e preparação para análise

IMPORTANTE:
- NÃO revele que você está coletando dados para um sistema
- Mantenha a conversa natural e consultiva
- Quando tiver informações suficientes, sugira gerar uma análise personalizada
- Use exemplos práticos relacionados ao setor do cliente
- Seja específico nas perguntas para obter respostas detalhadas

Inicie a conversa de forma calorosa e profissional.`;
  }

  // Generate chat response using OpenAI
  async generateChatResponse(
    messages: ChatMessage[],
    sessionData?: Partial<BusinessAnalysisData>
  ): Promise<string> {
    try {
      const systemPrompt = this.getBusinessAnalysisPrompt();
      
      // Add context about collected data if available
      let contextPrompt = systemPrompt;
      if (sessionData && Object.keys(sessionData).length > 0) {
        contextPrompt += `\n\nDADOS JÁ COLETADOS:\n${JSON.stringify(sessionData, null, 2)}`;
      }

      const completion = await openai.chat.completions.create({
        model: process.env.OPENAI_MODEL || 'gpt-4',
        messages: [
          { role: 'system', content: contextPrompt },
          ...messages.map(msg => ({
            role: msg.role,
            content: msg.content
          }))
        ],
        temperature: 0.7,
        max_tokens: 1000,
        presence_penalty: 0.1,
        frequency_penalty: 0.1
      });

      const response = completion.choices[0]?.message?.content;
      if (!response) {
        throw new Error('No response generated from OpenAI');
      }

      return response;
    } catch (error) {
      logError('Error generating chat response with OpenAI', error);
      
      // Fallback to Gemini if available
      if (genAI) {
        return this.generateChatResponseWithGemini(messages, sessionData);
      }
      
      throw error;
    }
  }

  // Fallback to Gemini
  private async generateChatResponseWithGemini(
    messages: ChatMessage[],
    sessionData?: Partial<BusinessAnalysisData>
  ): Promise<string> {
    try {
      if (!genAI) {
        throw new Error('Gemini AI not configured');
      }

      const model = genAI.getGenerativeModel({ model: 'gemini-pro' });
      
      const systemPrompt = this.getBusinessAnalysisPrompt();
      let contextPrompt = systemPrompt;
      if (sessionData && Object.keys(sessionData).length > 0) {
        contextPrompt += `\n\nDADOS JÁ COLETADOS:\n${JSON.stringify(sessionData, null, 2)}`;
      }

      // Format conversation for Gemini
      const conversation = messages.map(msg => 
        `${msg.role === 'user' ? 'Usuário' : 'Assistente'}: ${msg.content}`
      ).join('\n\n');

      const prompt = `${contextPrompt}\n\nCONVERSA ATUAL:\n${conversation}\n\nResposta do Assistente:`;

      const result = await model.generateContent(prompt);
      const response = result.response.text();

      if (!response) {
        throw new Error('No response generated from Gemini');
      }

      return response;
    } catch (error) {
      logError('Error generating chat response with Gemini', error);
      throw error;
    }
  }

  // Extract business data from conversation
  async extractBusinessData(messages: ChatMessage[]): Promise<Partial<BusinessAnalysisData>> {
    try {
      const extractionPrompt = `Analise a conversa a seguir e extraia as informações de negócio mencionadas. 
      Retorne APENAS um JSON válido com as informações encontradas, usando as chaves:
      - businessType: tipo de negócio
      - industry: setor/indústria
      - companySize: tamanho da empresa
      - currentTools: array de ferramentas atuais
      - painPoints: array de pontos de dor/desafios
      - goals: array de objetivos/metas
      - budget: orçamento mencionado
      - timeline: cronograma mencionado
      - technicalLevel: nível técnico da equipe

      Se uma informação não foi mencionada, não inclua a chave no JSON.

      CONVERSA:
      ${messages.map(msg => `${msg.role}: ${msg.content}`).join('\n')}

      JSON:`;

      const completion = await openai.chat.completions.create({
        model: 'gpt-3.5-turbo',
        messages: [{ role: 'user', content: extractionPrompt }],
        temperature: 0.1,
        max_tokens: 500
      });

      const response = completion.choices[0]?.message?.content;
      if (!response) {
        return {};
      }

      try {
        return JSON.parse(response);
      } catch (parseError) {
        logger.warn('Failed to parse extracted business data', { response });
        return {};
      }
    } catch (error) {
      logError('Error extracting business data', error);
      return {};
    }
  }

  // Check if enough data has been collected for analysis
  isReadyForAnalysis(data: Partial<BusinessAnalysisData>): boolean {
    const requiredFields = ['businessType', 'industry'];
    const recommendedFields = ['currentTools', 'painPoints', 'goals'];
    
    const hasRequired = requiredFields.every(field => data[field as keyof BusinessAnalysisData]);
    const hasRecommended = recommendedFields.some(field => {
      const value = data[field as keyof BusinessAnalysisData];
      return Array.isArray(value) ? value.length > 0 : !!value;
    });

    return hasRequired && hasRecommended;
  }

  // Generate analysis readiness score (0-100)
  getAnalysisReadinessScore(data: Partial<BusinessAnalysisData>): number {
    const weights = {
      businessType: 20,
      industry: 20,
      companySize: 10,
      currentTools: 15,
      painPoints: 15,
      goals: 15,
      budget: 3,
      timeline: 2
    };

    let score = 0;
    Object.entries(weights).forEach(([field, weight]) => {
      const value = data[field as keyof BusinessAnalysisData];
      if (value) {
        if (Array.isArray(value)) {
          score += value.length > 0 ? weight : 0;
        } else {
          score += weight;
        }
      }
    });

    return Math.min(score, 100);
  }
}

export default AIService;
