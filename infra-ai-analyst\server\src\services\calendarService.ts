import { google } from 'googleapis';
import { logger, logError } from '../utils/logger';

export interface CreateEventData {
  title: string;
  description: string;
  startTime: Date;
  duration: number; // in minutes
  attendeeEmail: string;
  attendeeName: string;
  timezone?: string;
}

export interface CalendarEvent {
  id: string;
  title: string;
  startTime: Date;
  endTime: Date;
  meetingUrl?: string;
  attendees: string[];
}

class CalendarService {
  private calendar: any;
  private auth: any;

  constructor() {
    this.initializeAuth();
  }

  private async initializeAuth() {
    try {
      // Initialize Google Auth
      this.auth = new google.auth.GoogleAuth({
        keyFile: process.env.GOOGLE_APPLICATION_CREDENTIALS,
        scopes: [
          'https://www.googleapis.com/auth/calendar',
          'https://www.googleapis.com/auth/calendar.events'
        ]
      });

      // Initialize Calendar API
      this.calendar = google.calendar({ version: 'v3', auth: this.auth });
      
      logger.info('Calendar service initialized successfully');
    } catch (error) {
      logError('Failed to initialize calendar service', error);
    }
  }

  // Create a calendar event
  async createEvent(eventData: CreateEventData): Promise<CalendarEvent> {
    try {
      if (!this.calendar) {
        throw new Error('Calendar service not initialized');
      }

      const endTime = new Date(eventData.startTime.getTime() + eventData.duration * 60000);
      
      const event = {
        summary: eventData.title,
        description: eventData.description,
        start: {
          dateTime: eventData.startTime.toISOString(),
          timeZone: eventData.timezone || 'America/Sao_Paulo'
        },
        end: {
          dateTime: endTime.toISOString(),
          timeZone: eventData.timezone || 'America/Sao_Paulo'
        },
        attendees: [
          {
            email: eventData.attendeeEmail,
            displayName: eventData.attendeeName,
            responseStatus: 'needsAction'
          },
          {
            email: process.env.ADMIN_EMAIL,
            displayName: 'Equipe Infra AI Analyst',
            responseStatus: 'accepted'
          }
        ],
        conferenceData: {
          createRequest: {
            requestId: `meet-${Date.now()}`,
            conferenceSolutionKey: {
              type: 'hangoutsMeet'
            }
          }
        },
        reminders: {
          useDefault: false,
          overrides: [
            { method: 'email', minutes: 24 * 60 }, // 1 day before
            { method: 'email', minutes: 60 },      // 1 hour before
            { method: 'popup', minutes: 15 }       // 15 minutes before
          ]
        },
        guestsCanModify: false,
        guestsCanInviteOthers: false,
        guestsCanSeeOtherGuests: false
      };

      const response = await this.calendar.events.insert({
        calendarId: process.env.GOOGLE_CALENDAR_ID || 'primary',
        resource: event,
        conferenceDataVersion: 1,
        sendUpdates: 'all'
      });

      const createdEvent = response.data;
      
      logger.info('Calendar event created successfully', {
        eventId: createdEvent.id,
        attendee: eventData.attendeeEmail,
        startTime: eventData.startTime
      });

      return {
        id: createdEvent.id,
        title: createdEvent.summary,
        startTime: new Date(createdEvent.start.dateTime),
        endTime: new Date(createdEvent.end.dateTime),
        meetingUrl: createdEvent.conferenceData?.entryPoints?.[0]?.uri,
        attendees: createdEvent.attendees?.map((a: any) => a.email) || []
      };
    } catch (error) {
      logError('Error creating calendar event', error);
      throw error;
    }
  }

  // Update an existing event
  async updateEvent(eventId: string, updates: Partial<CreateEventData>): Promise<CalendarEvent> {
    try {
      if (!this.calendar) {
        throw new Error('Calendar service not initialized');
      }

      // Get existing event
      const existingEvent = await this.calendar.events.get({
        calendarId: process.env.GOOGLE_CALENDAR_ID || 'primary',
        eventId
      });

      const event = existingEvent.data;
      
      // Apply updates
      if (updates.title) {
        event.summary = updates.title;
      }
      
      if (updates.description) {
        event.description = updates.description;
      }
      
      if (updates.startTime && updates.duration) {
        const endTime = new Date(updates.startTime.getTime() + updates.duration * 60000);
        event.start.dateTime = updates.startTime.toISOString();
        event.end.dateTime = endTime.toISOString();
      }

      const response = await this.calendar.events.update({
        calendarId: process.env.GOOGLE_CALENDAR_ID || 'primary',
        eventId,
        resource: event,
        sendUpdates: 'all'
      });

      const updatedEvent = response.data;
      
      logger.info('Calendar event updated successfully', { eventId });

      return {
        id: updatedEvent.id,
        title: updatedEvent.summary,
        startTime: new Date(updatedEvent.start.dateTime),
        endTime: new Date(updatedEvent.end.dateTime),
        meetingUrl: updatedEvent.conferenceData?.entryPoints?.[0]?.uri,
        attendees: updatedEvent.attendees?.map((a: any) => a.email) || []
      };
    } catch (error) {
      logError('Error updating calendar event', error);
      throw error;
    }
  }

  // Cancel an event
  async cancelEvent(eventId: string): Promise<void> {
    try {
      if (!this.calendar) {
        throw new Error('Calendar service not initialized');
      }

      await this.calendar.events.delete({
        calendarId: process.env.GOOGLE_CALENDAR_ID || 'primary',
        eventId,
        sendUpdates: 'all'
      });

      logger.info('Calendar event cancelled successfully', { eventId });
    } catch (error) {
      logError('Error cancelling calendar event', error);
      throw error;
    }
  }

  // Get event details
  async getEvent(eventId: string): Promise<CalendarEvent | null> {
    try {
      if (!this.calendar) {
        throw new Error('Calendar service not initialized');
      }

      const response = await this.calendar.events.get({
        calendarId: process.env.GOOGLE_CALENDAR_ID || 'primary',
        eventId
      });

      const event = response.data;

      return {
        id: event.id,
        title: event.summary,
        startTime: new Date(event.start.dateTime),
        endTime: new Date(event.end.dateTime),
        meetingUrl: event.conferenceData?.entryPoints?.[0]?.uri,
        attendees: event.attendees?.map((a: any) => a.email) || []
      };
    } catch (error) {
      if (error.code === 404) {
        return null;
      }
      logError('Error getting calendar event', error);
      throw error;
    }
  }

  // Get available time slots
  async getAvailableSlots(
    startDate: Date,
    endDate: Date,
    duration: number = 60,
    timezone: string = 'America/Sao_Paulo'
  ): Promise<Date[]> {
    try {
      if (!this.calendar) {
        throw new Error('Calendar service not initialized');
      }

      // Get busy times
      const response = await this.calendar.freebusy.query({
        resource: {
          timeMin: startDate.toISOString(),
          timeMax: endDate.toISOString(),
          timeZone: timezone,
          items: [
            { id: process.env.GOOGLE_CALENDAR_ID || 'primary' }
          ]
        }
      });

      const busyTimes = response.data.calendars[process.env.GOOGLE_CALENDAR_ID || 'primary']?.busy || [];
      
      // Generate available slots
      const availableSlots: Date[] = [];
      const workingHours = { start: 9, end: 18 }; // 9 AM to 6 PM
      
      for (let date = new Date(startDate); date < endDate; date.setDate(date.getDate() + 1)) {
        // Skip weekends
        if (date.getDay() === 0 || date.getDay() === 6) continue;
        
        for (let hour = workingHours.start; hour < workingHours.end; hour++) {
          const slotStart = new Date(date);
          slotStart.setHours(hour, 0, 0, 0);
          
          const slotEnd = new Date(slotStart.getTime() + duration * 60000);
          
          // Check if slot conflicts with busy times
          const isAvailable = !busyTimes.some((busy: any) => {
            const busyStart = new Date(busy.start);
            const busyEnd = new Date(busy.end);
            return slotStart < busyEnd && slotEnd > busyStart;
          });
          
          if (isAvailable && slotStart > new Date()) {
            availableSlots.push(new Date(slotStart));
          }
        }
      }

      return availableSlots.slice(0, 20); // Return first 20 available slots
    } catch (error) {
      logError('Error getting available slots', error);
      throw error;
    }
  }

  // Check if service is available
  isAvailable(): boolean {
    return !!this.calendar && !!process.env.GOOGLE_APPLICATION_CREDENTIALS;
  }
}

export const calendarService = new CalendarService();
