import nodemailer from 'nodemailer';
import { logger, logError } from '../utils/logger';

export interface LeadNotificationData {
  leadName: string;
  leadEmail: string;
  leadPhone?: string;
  leadCompany?: string;
  sessionId: string;
  businessData: any;
  conversationTranscript: string;
  capturedAt: string;
}

export interface AnalysisNotificationData {
  leadName: string;
  leadEmail: string;
  analysisId: string;
  businessType: string;
  industry: string;
  n8nJsonUrl?: string;
  createdAt: string;
}

export interface MeetingConfirmationData {
  meetingId: string;
  clientName: string;
  clientEmail: string;
  meetingTitle: string;
  scheduledAt: Date;
  duration: number;
  meetingUrl?: string;
  meetingType: string;
  businessType: string;
  industry: string;
}

export interface PaymentConfirmationData {
  paymentId: string;
  customerName: string;
  customerEmail: string;
  amount: number;
  currency: string;
  description: string;
  businessType?: string;
  industry?: string;
}

class EmailService {
  private transporter: nodemailer.Transporter;

  constructor() {
    this.transporter = nodemailer.createTransporter({
      host: process.env.SMTP_HOST,
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: process.env.SMTP_SECURE === 'true',
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS
      }
    });

    // Verify connection
    this.verifyConnection();
  }

  private async verifyConnection() {
    try {
      await this.transporter.verify();
      logger.info('Email service connected successfully');
    } catch (error) {
      logError('Email service connection failed', error);
    }
  }

  // Send lead notification to admin
  async sendLeadNotification(data: LeadNotificationData): Promise<void> {
    try {
      const htmlContent = this.generateLeadNotificationHTML(data);
      
      await this.transporter.sendMail({
        from: `"${process.env.FROM_NAME}" <${process.env.FROM_EMAIL}>`,
        to: process.env.ADMIN_EMAIL,
        cc: process.env.NOTIFICATION_EMAIL,
        subject: `🎯 Novo Lead Capturado: ${data.leadName}`,
        html: htmlContent,
        attachments: [
          {
            filename: `conversa-${data.sessionId}.txt`,
            content: data.conversationTranscript,
            contentType: 'text/plain'
          }
        ]
      });

      logger.info('Lead notification email sent', {
        leadEmail: data.leadEmail,
        sessionId: data.sessionId
      });
    } catch (error) {
      logError('Failed to send lead notification email', error);
      throw error;
    }
  }

  // Send analysis completion notification
  async sendAnalysisNotification(data: AnalysisNotificationData): Promise<void> {
    try {
      const htmlContent = this.generateAnalysisNotificationHTML(data);
      
      await this.transporter.sendMail({
        from: `"${process.env.FROM_NAME}" <${process.env.FROM_EMAIL}>`,
        to: process.env.ADMIN_EMAIL,
        cc: process.env.NOTIFICATION_EMAIL,
        subject: `📊 Nova Análise Gerada: ${data.leadName} - ${data.businessType}`,
        html: htmlContent
      });

      logger.info('Analysis notification email sent', {
        leadEmail: data.leadEmail,
        analysisId: data.analysisId
      });
    } catch (error) {
      logError('Failed to send analysis notification email', error);
      throw error;
    }
  }

  // Send welcome email to lead
  async sendWelcomeEmail(leadEmail: string, leadName: string): Promise<void> {
    try {
      const htmlContent = this.generateWelcomeEmailHTML(leadName);

      await this.transporter.sendMail({
        from: `"${process.env.FROM_NAME}" <${process.env.FROM_EMAIL}>`,
        to: leadEmail,
        subject: `Bem-vindo ao Infra AI Analyst, ${leadName}!`,
        html: htmlContent
      });

      logger.info('Welcome email sent', { leadEmail });
    } catch (error) {
      logError('Failed to send welcome email', error);
      throw error;
    }
  }

  // Send meeting confirmation email
  async sendMeetingConfirmation(data: MeetingConfirmationData): Promise<void> {
    try {
      const htmlContent = this.generateMeetingConfirmationHTML(data);

      // Send to client
      await this.transporter.sendMail({
        from: `"${process.env.FROM_NAME}" <${process.env.FROM_EMAIL}>`,
        to: data.clientEmail,
        cc: process.env.ADMIN_EMAIL,
        subject: `✅ Reunião Confirmada: ${data.meetingTitle}`,
        html: htmlContent
      });

      // Send notification to admin
      await this.transporter.sendMail({
        from: `"${process.env.FROM_NAME}" <${process.env.FROM_EMAIL}>`,
        to: process.env.ADMIN_EMAIL,
        subject: `📅 Nova Reunião Agendada: ${data.clientName}`,
        html: this.generateMeetingAdminNotificationHTML(data)
      });

      logger.info('Meeting confirmation emails sent', {
        meetingId: data.meetingId,
        clientEmail: data.clientEmail
      });
    } catch (error) {
      logError('Failed to send meeting confirmation emails', error);
      throw error;
    }
  }

  // Send payment confirmation email
  async sendPaymentConfirmation(data: PaymentConfirmationData): Promise<void> {
    try {
      const htmlContent = this.generatePaymentConfirmationHTML(data);

      // Send to customer
      await this.transporter.sendMail({
        from: `"${process.env.FROM_NAME}" <${process.env.FROM_EMAIL}>`,
        to: data.customerEmail,
        cc: process.env.ADMIN_EMAIL,
        subject: `✅ Pagamento Confirmado - ${data.description}`,
        html: htmlContent
      });

      // Send notification to admin
      await this.transporter.sendMail({
        from: `"${process.env.FROM_NAME}" <${process.env.FROM_EMAIL}>`,
        to: process.env.ADMIN_EMAIL,
        subject: `💰 Pagamento Recebido: ${data.customerName}`,
        html: this.generatePaymentAdminNotificationHTML(data)
      });

      logger.info('Payment confirmation emails sent', {
        paymentId: data.paymentId,
        customerEmail: data.customerEmail
      });
    } catch (error) {
      logError('Failed to send payment confirmation emails', error);
      throw error;
    }
  }

  // Generate lead notification HTML
  private generateLeadNotificationHTML(data: LeadNotificationData): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px 8px 0 0; }
          .content { background: #f8f9fa; padding: 20px; border-radius: 0 0 8px 8px; }
          .info-box { background: white; padding: 15px; margin: 10px 0; border-radius: 6px; border-left: 4px solid #667eea; }
          .business-data { background: #e3f2fd; padding: 15px; margin: 10px 0; border-radius: 6px; }
          .footer { text-align: center; margin-top: 20px; color: #666; font-size: 12px; }
          .btn { display: inline-block; padding: 12px 24px; background: #667eea; color: white; text-decoration: none; border-radius: 6px; margin: 10px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🎯 Novo Lead Capturado!</h1>
            <p>Um novo lead foi capturado no sistema Infra AI Analyst</p>
          </div>
          
          <div class="content">
            <div class="info-box">
              <h3>📋 Informações do Lead</h3>
              <p><strong>Nome:</strong> ${data.leadName}</p>
              <p><strong>Email:</strong> ${data.leadEmail}</p>
              ${data.leadPhone ? `<p><strong>Telefone:</strong> ${data.leadPhone}</p>` : ''}
              ${data.leadCompany ? `<p><strong>Empresa:</strong> ${data.leadCompany}</p>` : ''}
              <p><strong>Capturado em:</strong> ${new Date(data.capturedAt).toLocaleString('pt-BR')}</p>
              <p><strong>ID da Sessão:</strong> ${data.sessionId}</p>
            </div>

            ${Object.keys(data.businessData).length > 0 ? `
            <div class="business-data">
              <h3>🏢 Dados do Negócio Coletados</h3>
              ${data.businessData.businessType ? `<p><strong>Tipo de Negócio:</strong> ${data.businessData.businessType}</p>` : ''}
              ${data.businessData.industry ? `<p><strong>Setor:</strong> ${data.businessData.industry}</p>` : ''}
              ${data.businessData.companySize ? `<p><strong>Tamanho da Empresa:</strong> ${data.businessData.companySize}</p>` : ''}
              ${data.businessData.currentTools && data.businessData.currentTools.length > 0 ? `<p><strong>Ferramentas Atuais:</strong> ${data.businessData.currentTools.join(', ')}</p>` : ''}
              ${data.businessData.painPoints && data.businessData.painPoints.length > 0 ? `<p><strong>Pontos de Dor:</strong> ${data.businessData.painPoints.join(', ')}</p>` : ''}
              ${data.businessData.goals && data.businessData.goals.length > 0 ? `<p><strong>Objetivos:</strong> ${data.businessData.goals.join(', ')}</p>` : ''}
            </div>
            ` : ''}

            <div class="info-box">
              <h3>💬 Próximos Passos</h3>
              <p>1. Revisar a conversa completa (anexada)</p>
              <p>2. Gerar análise personalizada</p>
              <p>3. Entrar em contato para agendar reunião</p>
              <p>4. Preparar proposta comercial</p>
            </div>

            <div style="text-align: center;">
              <a href="${process.env.ADMIN_URL}/leads/${data.sessionId}" class="btn">
                Ver Detalhes no Painel Admin
              </a>
            </div>
          </div>

          <div class="footer">
            <p>Infra AI Analyst - Sistema de Análise de Negócios com IA</p>
            <p>Este email foi gerado automaticamente pelo sistema.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  // Generate analysis notification HTML
  private generateAnalysisNotificationHTML(data: AnalysisNotificationData): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%); color: white; padding: 20px; border-radius: 8px 8px 0 0; }
          .content { background: #f8f9fa; padding: 20px; border-radius: 0 0 8px 8px; }
          .info-box { background: white; padding: 15px; margin: 10px 0; border-radius: 6px; border-left: 4px solid #4CAF50; }
          .btn { display: inline-block; padding: 12px 24px; background: #4CAF50; color: white; text-decoration: none; border-radius: 6px; margin: 10px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>📊 Nova Análise Gerada!</h1>
            <p>Uma análise completa foi gerada para o lead ${data.leadName}</p>
          </div>
          
          <div class="content">
            <div class="info-box">
              <h3>📋 Detalhes da Análise</h3>
              <p><strong>Cliente:</strong> ${data.leadName} (${data.leadEmail})</p>
              <p><strong>Tipo de Negócio:</strong> ${data.businessType}</p>
              <p><strong>Setor:</strong> ${data.industry}</p>
              <p><strong>Gerada em:</strong> ${new Date(data.createdAt).toLocaleString('pt-BR')}</p>
              <p><strong>ID da Análise:</strong> ${data.analysisId}</p>
            </div>

            ${data.n8nJsonUrl ? `
            <div class="info-box">
              <h3>🔧 Arquivo N8N Gerado</h3>
              <p>O arquivo JSON para automação N8N foi gerado automaticamente.</p>
              <a href="${data.n8nJsonUrl}" class="btn">Download do Arquivo N8N</a>
            </div>
            ` : ''}

            <div style="text-align: center;">
              <a href="${process.env.ADMIN_URL}/analyses/${data.analysisId}" class="btn">
                Ver Análise Completa
              </a>
            </div>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  // Generate welcome email HTML
  private generateWelcomeEmailHTML(leadName: string): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px 8px 0 0; text-align: center; }
          .content { background: #f8f9fa; padding: 20px; border-radius: 0 0 8px 8px; }
          .btn { display: inline-block; padding: 12px 24px; background: #667eea; color: white; text-decoration: none; border-radius: 6px; margin: 10px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🎉 Bem-vindo ao Infra AI Analyst!</h1>
            <p>Olá, ${leadName}! Obrigado por seu interesse em nossos serviços.</p>
          </div>

          <div class="content">
            <p>Sua análise personalizada está sendo preparada por nossa equipe de especialistas.</p>
            <p>Em breve, você receberá um plano detalhado de automação para seu negócio.</p>

            <div style="text-align: center;">
              <a href="${process.env.SITE_URL}" class="btn">Acessar Plataforma</a>
            </div>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  // Generate meeting confirmation email HTML
  private generateMeetingConfirmationHTML(data: MeetingConfirmationData): string {
    const meetingDate = data.scheduledAt.toLocaleDateString('pt-BR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });

    const meetingTime = data.scheduledAt.toLocaleTimeString('pt-BR', {
      hour: '2-digit',
      minute: '2-digit'
    });

    const endTime = new Date(data.scheduledAt.getTime() + data.duration * 60000).toLocaleTimeString('pt-BR', {
      hour: '2-digit',
      minute: '2-digit'
    });

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%); color: white; padding: 20px; border-radius: 8px 8px 0 0; }
          .content { background: #f8f9fa; padding: 20px; border-radius: 0 0 8px 8px; }
          .meeting-details { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #4CAF50; }
          .btn { display: inline-block; padding: 12px 24px; background: #4CAF50; color: white; text-decoration: none; border-radius: 6px; margin: 10px 5px; }
          .btn-secondary { background: #2196F3; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>✅ Reunião Confirmada!</h1>
            <p>Sua reunião foi agendada com sucesso</p>
          </div>

          <div class="content">
            <p>Olá ${data.clientName}!</p>

            <p>Confirmamos o agendamento da sua reunião conosco. Estamos ansiosos para discutir como podemos ajudar a automatizar e otimizar os processos da sua empresa no setor de ${data.industry}.</p>

            <div class="meeting-details">
              <h3>📅 Detalhes da Reunião</h3>
              <p><strong>Título:</strong> ${data.meetingTitle}</p>
              <p><strong>Data:</strong> ${meetingDate}</p>
              <p><strong>Horário:</strong> ${meetingTime} às ${endTime} (horário de Brasília)</p>
              <p><strong>Duração:</strong> ${data.duration} minutos</p>
              <p><strong>Tipo:</strong> ${this.getMeetingTypeLabel(data.meetingType)}</p>
              ${data.meetingUrl ? `<p><strong>Link da Reunião:</strong> <a href="${data.meetingUrl}">${data.meetingUrl}</a></p>` : ''}
            </div>

            <h3>📋 O que vamos abordar:</h3>
            ${this.getMeetingAgenda(data.meetingType)}

            <h3>📝 Preparação para a reunião:</h3>
            <ul>
              <li>Tenha em mãos informações sobre seus processos atuais</li>
              <li>Liste os principais desafios que enfrenta</li>
              <li>Pense nos objetivos que gostaria de alcançar</li>
              <li>Prepare perguntas sobre automação</li>
            </ul>

            <div style="text-align: center; margin: 30px 0;">
              ${data.meetingUrl ? `<a href="${data.meetingUrl}" class="btn">Entrar na Reunião</a>` : ''}
              <a href="${process.env.SITE_URL}/calendar/add?meeting=${data.meetingId}" class="btn btn-secondary">Adicionar ao Calendário</a>
            </div>

            <p><strong>Precisa reagendar?</strong> Responda este email ou entre em contato conosco.</p>

            <p>Até breve!</p>
            <p>Equipe Infra AI Analyst</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  // Generate admin notification for new meeting
  private generateMeetingAdminNotificationHTML(data: MeetingConfirmationData): string {
    const meetingDate = data.scheduledAt.toLocaleDateString('pt-BR');
    const meetingTime = data.scheduledAt.toLocaleTimeString('pt-BR', {
      hour: '2-digit',
      minute: '2-digit'
    });

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%); color: white; padding: 20px; border-radius: 8px 8px 0 0; }
          .content { background: #f8f9fa; padding: 20px; border-radius: 0 0 8px 8px; }
          .info-box { background: white; padding: 15px; margin: 10px 0; border-radius: 6px; border-left: 4px solid #2196F3; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>📅 Nova Reunião Agendada</h1>
            <p>Um cliente agendou uma reunião</p>
          </div>

          <div class="content">
            <div class="info-box">
              <h3>👤 Informações do Cliente</h3>
              <p><strong>Nome:</strong> ${data.clientName}</p>
              <p><strong>Email:</strong> ${data.clientEmail}</p>
              <p><strong>Tipo de Negócio:</strong> ${data.businessType}</p>
              <p><strong>Setor:</strong> ${data.industry}</p>
            </div>

            <div class="info-box">
              <h3>📅 Detalhes da Reunião</h3>
              <p><strong>Título:</strong> ${data.meetingTitle}</p>
              <p><strong>Data:</strong> ${meetingDate}</p>
              <p><strong>Horário:</strong> ${meetingTime}</p>
              <p><strong>Duração:</strong> ${data.duration} minutos</p>
              <p><strong>Tipo:</strong> ${this.getMeetingTypeLabel(data.meetingType)}</p>
              <p><strong>ID da Reunião:</strong> ${data.meetingId}</p>
              ${data.meetingUrl ? `<p><strong>Link:</strong> <a href="${data.meetingUrl}">${data.meetingUrl}</a></p>` : ''}
            </div>

            <p><strong>Próximos passos:</strong></p>
            <ol>
              <li>Revisar a análise do cliente</li>
              <li>Preparar apresentação personalizada</li>
              <li>Confirmar presença na reunião</li>
            </ol>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  // Helper methods
  private getMeetingTypeLabel(type: string): string {
    const labels = {
      consultation: 'Consultoria Inicial',
      implementation: 'Reunião de Implementação',
      'follow-up': 'Follow-up de Projeto'
    };
    return labels[type as keyof typeof labels] || type;
  }

  private getMeetingAgenda(type: string): string {
    const agendas = {
      consultation: `
        <ul>
          <li>Apresentação da análise personalizada</li>
          <li>Discussão das oportunidades de automação</li>
          <li>Esclarecimento de dúvidas técnicas</li>
          <li>Definição de próximos passos</li>
          <li>Apresentação da proposta comercial</li>
        </ul>
      `,
      implementation: `
        <ul>
          <li>Revisão do plano de implementação</li>
          <li>Configuração técnica inicial</li>
          <li>Treinamento da equipe</li>
          <li>Definição de cronograma detalhado</li>
          <li>Setup de acompanhamento e suporte</li>
        </ul>
      `,
      'follow-up': `
        <ul>
          <li>Revisão do progresso atual</li>
          <li>Identificação de desafios e soluções</li>
          <li>Ajustes necessários no projeto</li>
          <li>Planejamento das próximas etapas</li>
          <li>Coleta de feedback e melhorias</li>
        </ul>
      `
    };
    return agendas[type as keyof typeof agendas] || '<p>Agenda personalizada será definida.</p>';
  }

  // Generate payment confirmation email HTML
  private generatePaymentConfirmationHTML(data: PaymentConfirmationData): string {
    const formattedAmount = new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: data.currency
    }).format(data.amount);

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%); color: white; padding: 20px; border-radius: 8px 8px 0 0; }
          .content { background: #f8f9fa; padding: 20px; border-radius: 0 0 8px 8px; }
          .payment-details { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #4CAF50; }
          .success-badge { background: #4CAF50; color: white; padding: 8px 16px; border-radius: 20px; display: inline-block; font-weight: bold; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>✅ Pagamento Confirmado!</h1>
            <p>Seu pagamento foi processado com sucesso</p>
          </div>

          <div class="content">
            <p>Olá ${data.customerName}!</p>

            <div style="text-align: center; margin: 20px 0;">
              <span class="success-badge">PAGAMENTO APROVADO</span>
            </div>

            <p>Confirmamos o recebimento do seu pagamento. Obrigado por confiar em nossos serviços!</p>

            <div class="payment-details">
              <h3>💳 Detalhes do Pagamento</h3>
              <p><strong>Valor:</strong> ${formattedAmount}</p>
              <p><strong>Descrição:</strong> ${data.description}</p>
              <p><strong>ID do Pagamento:</strong> ${data.paymentId}</p>
              <p><strong>Data:</strong> ${new Date().toLocaleDateString('pt-BR')}</p>
              ${data.businessType ? `<p><strong>Projeto:</strong> ${data.businessType} - ${data.industry}</p>` : ''}
            </div>

            <h3>📋 Próximos Passos:</h3>
            <ol>
              <li>Nossa equipe iniciará o trabalho em até 24 horas</li>
              <li>Você receberá atualizações regulares sobre o progresso</li>
              <li>Um especialista entrará em contato para alinhar detalhes</li>
              <li>Implementação será iniciada conforme cronograma acordado</li>
            </ol>

            <div style="background: #e3f2fd; padding: 15px; border-radius: 8px; margin: 20px 0;">
              <h4>📞 Precisa de Suporte?</h4>
              <p>Nossa equipe está disponível para esclarecer qualquer dúvida:</p>
              <p>📧 Email: ${process.env.SUPPORT_EMAIL || '<EMAIL>'}</p>
              <p>📱 WhatsApp: ${process.env.SUPPORT_PHONE || '+55 11 99999-9999'}</p>
            </div>

            <p>Obrigado por escolher a Infra AI Analyst para transformar seu negócio!</p>

            <p>Atenciosamente,<br>
            Equipe Infra AI Analyst</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  // Generate admin notification for payment
  private generatePaymentAdminNotificationHTML(data: PaymentConfirmationData): string {
    const formattedAmount = new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: data.currency
    }).format(data.amount);

    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%); color: white; padding: 20px; border-radius: 8px 8px 0 0; }
          .content { background: #f8f9fa; padding: 20px; border-radius: 0 0 8px 8px; }
          .info-box { background: white; padding: 15px; margin: 10px 0; border-radius: 6px; border-left: 4px solid #4CAF50; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>💰 Pagamento Recebido</h1>
            <p>Um cliente efetuou um pagamento</p>
          </div>

          <div class="content">
            <div class="info-box">
              <h3>👤 Informações do Cliente</h3>
              <p><strong>Nome:</strong> ${data.customerName}</p>
              <p><strong>Email:</strong> ${data.customerEmail}</p>
              ${data.businessType ? `<p><strong>Tipo de Negócio:</strong> ${data.businessType}</p>` : ''}
              ${data.industry ? `<p><strong>Setor:</strong> ${data.industry}</p>` : ''}
            </div>

            <div class="info-box">
              <h3>💳 Detalhes do Pagamento</h3>
              <p><strong>Valor:</strong> ${formattedAmount}</p>
              <p><strong>Descrição:</strong> ${data.description}</p>
              <p><strong>ID do Pagamento:</strong> ${data.paymentId}</p>
              <p><strong>Data:</strong> ${new Date().toLocaleDateString('pt-BR')} às ${new Date().toLocaleTimeString('pt-BR')}</p>
            </div>

            <p><strong>Próximos passos:</strong></p>
            <ol>
              <li>Confirmar recebimento com o cliente</li>
              <li>Iniciar o projeto conforme escopo acordado</li>
              <li>Agendar reunião de kick-off se necessário</li>
              <li>Atualizar status no sistema de gestão</li>
            </ol>
          </div>
        </div>
      </body>
      </html>
    `;
  }
}

export const emailService = new EmailService();
