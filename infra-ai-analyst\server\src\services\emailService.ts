import nodemailer from 'nodemailer';
import { logger, logError } from '../utils/logger';

export interface LeadNotificationData {
  leadName: string;
  leadEmail: string;
  leadPhone?: string;
  leadCompany?: string;
  sessionId: string;
  businessData: any;
  conversationTranscript: string;
  capturedAt: string;
}

export interface AnalysisNotificationData {
  leadName: string;
  leadEmail: string;
  analysisId: string;
  businessType: string;
  industry: string;
  n8nJsonUrl?: string;
  createdAt: string;
}

class EmailService {
  private transporter: nodemailer.Transporter;

  constructor() {
    this.transporter = nodemailer.createTransporter({
      host: process.env.SMTP_HOST,
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: process.env.SMTP_SECURE === 'true',
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS
      }
    });

    // Verify connection
    this.verifyConnection();
  }

  private async verifyConnection() {
    try {
      await this.transporter.verify();
      logger.info('Email service connected successfully');
    } catch (error) {
      logError('Email service connection failed', error);
    }
  }

  // Send lead notification to admin
  async sendLeadNotification(data: LeadNotificationData): Promise<void> {
    try {
      const htmlContent = this.generateLeadNotificationHTML(data);
      
      await this.transporter.sendMail({
        from: `"${process.env.FROM_NAME}" <${process.env.FROM_EMAIL}>`,
        to: process.env.ADMIN_EMAIL,
        cc: process.env.NOTIFICATION_EMAIL,
        subject: `🎯 Novo Lead Capturado: ${data.leadName}`,
        html: htmlContent,
        attachments: [
          {
            filename: `conversa-${data.sessionId}.txt`,
            content: data.conversationTranscript,
            contentType: 'text/plain'
          }
        ]
      });

      logger.info('Lead notification email sent', {
        leadEmail: data.leadEmail,
        sessionId: data.sessionId
      });
    } catch (error) {
      logError('Failed to send lead notification email', error);
      throw error;
    }
  }

  // Send analysis completion notification
  async sendAnalysisNotification(data: AnalysisNotificationData): Promise<void> {
    try {
      const htmlContent = this.generateAnalysisNotificationHTML(data);
      
      await this.transporter.sendMail({
        from: `"${process.env.FROM_NAME}" <${process.env.FROM_EMAIL}>`,
        to: process.env.ADMIN_EMAIL,
        cc: process.env.NOTIFICATION_EMAIL,
        subject: `📊 Nova Análise Gerada: ${data.leadName} - ${data.businessType}`,
        html: htmlContent
      });

      logger.info('Analysis notification email sent', {
        leadEmail: data.leadEmail,
        analysisId: data.analysisId
      });
    } catch (error) {
      logError('Failed to send analysis notification email', error);
      throw error;
    }
  }

  // Send welcome email to lead
  async sendWelcomeEmail(leadEmail: string, leadName: string): Promise<void> {
    try {
      const htmlContent = this.generateWelcomeEmailHTML(leadName);
      
      await this.transporter.sendMail({
        from: `"${process.env.FROM_NAME}" <${process.env.FROM_EMAIL}>`,
        to: leadEmail,
        subject: `Bem-vindo ao Infra AI Analyst, ${leadName}!`,
        html: htmlContent
      });

      logger.info('Welcome email sent', { leadEmail });
    } catch (error) {
      logError('Failed to send welcome email', error);
      throw error;
    }
  }

  // Generate lead notification HTML
  private generateLeadNotificationHTML(data: LeadNotificationData): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px 8px 0 0; }
          .content { background: #f8f9fa; padding: 20px; border-radius: 0 0 8px 8px; }
          .info-box { background: white; padding: 15px; margin: 10px 0; border-radius: 6px; border-left: 4px solid #667eea; }
          .business-data { background: #e3f2fd; padding: 15px; margin: 10px 0; border-radius: 6px; }
          .footer { text-align: center; margin-top: 20px; color: #666; font-size: 12px; }
          .btn { display: inline-block; padding: 12px 24px; background: #667eea; color: white; text-decoration: none; border-radius: 6px; margin: 10px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🎯 Novo Lead Capturado!</h1>
            <p>Um novo lead foi capturado no sistema Infra AI Analyst</p>
          </div>
          
          <div class="content">
            <div class="info-box">
              <h3>📋 Informações do Lead</h3>
              <p><strong>Nome:</strong> ${data.leadName}</p>
              <p><strong>Email:</strong> ${data.leadEmail}</p>
              ${data.leadPhone ? `<p><strong>Telefone:</strong> ${data.leadPhone}</p>` : ''}
              ${data.leadCompany ? `<p><strong>Empresa:</strong> ${data.leadCompany}</p>` : ''}
              <p><strong>Capturado em:</strong> ${new Date(data.capturedAt).toLocaleString('pt-BR')}</p>
              <p><strong>ID da Sessão:</strong> ${data.sessionId}</p>
            </div>

            ${Object.keys(data.businessData).length > 0 ? `
            <div class="business-data">
              <h3>🏢 Dados do Negócio Coletados</h3>
              ${data.businessData.businessType ? `<p><strong>Tipo de Negócio:</strong> ${data.businessData.businessType}</p>` : ''}
              ${data.businessData.industry ? `<p><strong>Setor:</strong> ${data.businessData.industry}</p>` : ''}
              ${data.businessData.companySize ? `<p><strong>Tamanho da Empresa:</strong> ${data.businessData.companySize}</p>` : ''}
              ${data.businessData.currentTools && data.businessData.currentTools.length > 0 ? `<p><strong>Ferramentas Atuais:</strong> ${data.businessData.currentTools.join(', ')}</p>` : ''}
              ${data.businessData.painPoints && data.businessData.painPoints.length > 0 ? `<p><strong>Pontos de Dor:</strong> ${data.businessData.painPoints.join(', ')}</p>` : ''}
              ${data.businessData.goals && data.businessData.goals.length > 0 ? `<p><strong>Objetivos:</strong> ${data.businessData.goals.join(', ')}</p>` : ''}
            </div>
            ` : ''}

            <div class="info-box">
              <h3>💬 Próximos Passos</h3>
              <p>1. Revisar a conversa completa (anexada)</p>
              <p>2. Gerar análise personalizada</p>
              <p>3. Entrar em contato para agendar reunião</p>
              <p>4. Preparar proposta comercial</p>
            </div>

            <div style="text-align: center;">
              <a href="${process.env.ADMIN_URL}/leads/${data.sessionId}" class="btn">
                Ver Detalhes no Painel Admin
              </a>
            </div>
          </div>

          <div class="footer">
            <p>Infra AI Analyst - Sistema de Análise de Negócios com IA</p>
            <p>Este email foi gerado automaticamente pelo sistema.</p>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  // Generate analysis notification HTML
  private generateAnalysisNotificationHTML(data: AnalysisNotificationData): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%); color: white; padding: 20px; border-radius: 8px 8px 0 0; }
          .content { background: #f8f9fa; padding: 20px; border-radius: 0 0 8px 8px; }
          .info-box { background: white; padding: 15px; margin: 10px 0; border-radius: 6px; border-left: 4px solid #4CAF50; }
          .btn { display: inline-block; padding: 12px 24px; background: #4CAF50; color: white; text-decoration: none; border-radius: 6px; margin: 10px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>📊 Nova Análise Gerada!</h1>
            <p>Uma análise completa foi gerada para o lead ${data.leadName}</p>
          </div>
          
          <div class="content">
            <div class="info-box">
              <h3>📋 Detalhes da Análise</h3>
              <p><strong>Cliente:</strong> ${data.leadName} (${data.leadEmail})</p>
              <p><strong>Tipo de Negócio:</strong> ${data.businessType}</p>
              <p><strong>Setor:</strong> ${data.industry}</p>
              <p><strong>Gerada em:</strong> ${new Date(data.createdAt).toLocaleString('pt-BR')}</p>
              <p><strong>ID da Análise:</strong> ${data.analysisId}</p>
            </div>

            ${data.n8nJsonUrl ? `
            <div class="info-box">
              <h3>🔧 Arquivo N8N Gerado</h3>
              <p>O arquivo JSON para automação N8N foi gerado automaticamente.</p>
              <a href="${data.n8nJsonUrl}" class="btn">Download do Arquivo N8N</a>
            </div>
            ` : ''}

            <div style="text-align: center;">
              <a href="${process.env.ADMIN_URL}/analyses/${data.analysisId}" class="btn">
                Ver Análise Completa
              </a>
            </div>
          </div>
        </div>
      </body>
      </html>
    `;
  }

  // Generate welcome email HTML
  private generateWelcomeEmailHTML(leadName: string): string {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px 8px 0 0; text-align: center; }
          .content { background: #f8f9fa; padding: 20px; border-radius: 0 0 8px 8px; }
          .btn { display: inline-block; padding: 12px 24px; background: #667eea; color: white; text-decoration: none; border-radius: 6px; margin: 10px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🎉 Bem-vindo ao Infra AI Analyst!</h1>
            <p>Olá, ${leadName}! Obrigado por seu interesse em nossos serviços.</p>
          </div>
          
          <div class="content">
            <p>Sua análise personalizada está sendo preparada por nossa equipe de especialistas.</p>
            <p>Em breve, você receberá um plano detalhado de automação para seu negócio.</p>
            
            <div style="text-align: center;">
              <a href="${process.env.SITE_URL}" class="btn">Acessar Plataforma</a>
            </div>
          </div>
        </div>
      </body>
      </html>
    `;
  }
}

export const emailService = new EmailService();
