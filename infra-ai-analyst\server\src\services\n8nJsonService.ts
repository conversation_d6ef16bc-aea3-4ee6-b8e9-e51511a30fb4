import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';
import OpenAI from 'openai';
import { logger, logError } from '../utils/logger';
import { BusinessAnalysisData } from './aiService';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export class N8nJsonService {
  private templatesDir: string;
  private outputDir: string;
  private templates: any[] = [];

  constructor() {
    this.templatesDir = path.join(process.cwd(), 'agent-templates-base');
    this.outputDir = path.join(process.cwd(), process.env.UPLOAD_DIR || 'uploads', 'n8n');
    this.ensureOutputDir();
    this.loadTemplates();
  }

  // Ensure output directory exists
  private ensureOutputDir(): void {
    if (!fs.existsSync(this.outputDir)) {
      fs.mkdirSync(this.outputDir, { recursive: true });
      logger.info('Created N8N output directory', { path: this.outputDir });
    }
  }

  // Load all template files
  private loadTemplates(): void {
    try {
      const templateFiles = fs.readdirSync(this.templatesDir)
        .filter(file => file.endsWith('.json'));

      this.templates = templateFiles.map(file => {
        const filepath = path.join(this.templatesDir, file);
        const content = fs.readFileSync(filepath, 'utf8');
        return {
          name: file,
          content: JSON.parse(content)
        };
      });

      logger.info('Loaded N8N templates', { count: this.templates.length });
    } catch (error) {
      logError('Error loading N8N templates', error);
      this.templates = [];
    }
  }

  // Generate N8N JSON based on business analysis
  async generateN8nJson(
    businessData: BusinessAnalysisData,
    analysisResult: any,
    conversationHistory: string
  ): Promise<string> {
    try {
      logger.info('Starting N8N JSON generation', { businessType: businessData.businessType });

      // Select the most appropriate template
      const selectedTemplate = this.selectBestTemplate(businessData);

      // Generate customized N8N workflow
      const customizedWorkflow = await this.customizeWorkflow(
        selectedTemplate,
        businessData,
        analysisResult,
        conversationHistory
      );

      // Save the generated JSON
      const filename = `n8n_workflow_${businessData.businessType?.replace(/\s+/g, '_')}_${uuidv4()}.json`;
      const filepath = path.join(this.outputDir, filename);

      fs.writeFileSync(filepath, JSON.stringify(customizedWorkflow, null, 2));

      // Generate public URL
      const jsonUrl = `/uploads/n8n/${filename}`;

      logger.info('N8N JSON generation completed', { filename, url: jsonUrl });

      return jsonUrl;
    } catch (error) {
      logError('Error generating N8N JSON', error);
      throw error;
    }
  }

  // Select the best template based on business data
  private selectBestTemplate(businessData: BusinessAnalysisData): any {
    if (this.templates.length === 0) {
      throw new Error('No N8N templates available');
    }

    // Simple selection logic - can be enhanced with AI
    // For now, use Template 1 as default, but this can be improved
    let selectedTemplate = this.templates[0];

    // Basic selection based on business type
    const businessType = businessData.businessType?.toLowerCase() || '';
    const industry = businessData.industry?.toLowerCase() || '';

    if (businessType.includes('e-commerce') || businessType.includes('loja')) {
      // Use template optimized for e-commerce
      selectedTemplate = this.templates.find(t => t.name.includes('2')) || this.templates[0];
    } else if (businessType.includes('serviço') || industry.includes('consultoria')) {
      // Use template optimized for services
      selectedTemplate = this.templates.find(t => t.name.includes('3')) || this.templates[0];
    } else if (businessType.includes('saas') || businessType.includes('software')) {
      // Use template optimized for SaaS
      selectedTemplate = this.templates.find(t => t.name.includes('4')) || this.templates[0];
    }

    logger.info('Selected N8N template', { 
      template: selectedTemplate.name,
      businessType: businessData.businessType 
    });

    return selectedTemplate;
  }

  // Customize workflow using AI
  private async customizeWorkflow(
    template: any,
    businessData: BusinessAnalysisData,
    analysisResult: any,
    conversationHistory: string
  ): Promise<any> {
    try {
      const customizationPrompt = this.buildCustomizationPrompt(
        template,
        businessData,
        analysisResult,
        conversationHistory
      );

      const completion = await openai.chat.completions.create({
        model: 'gpt-4',
        messages: [{ role: 'user', content: customizationPrompt }],
        temperature: 0.3, // Lower temperature for more consistent JSON
        max_tokens: 4000
      });

      const response = completion.choices[0]?.message?.content;
      if (!response) {
        throw new Error('No response from AI for workflow customization');
      }

      // Try to parse the JSON response
      try {
        const customizedWorkflow = JSON.parse(response);
        return this.validateAndCleanWorkflow(customizedWorkflow);
      } catch (parseError) {
        logger.warn('Failed to parse AI-generated JSON, using template with basic customization');
        return this.basicCustomization(template.content, businessData);
      }
    } catch (error) {
      logError('Error customizing workflow with AI', error);
      // Fallback to basic customization
      return this.basicCustomization(template.content, businessData);
    }
  }

  // Build prompt for AI customization
  private buildCustomizationPrompt(
    template: any,
    businessData: BusinessAnalysisData,
    analysisResult: any,
    conversationHistory: string
  ): string {
    const automationOpportunities = this.identifyAutomationOpportunities(businessData);
    const integrationSuggestions = this.suggestIntegrations(businessData);

    return `Você é um especialista em N8N e automação de processos empresariais. Sua tarefa é customizar um template de workflow N8N para criar uma solução de automação específica e prática para o negócio descrito.

TEMPLATE BASE N8N:
${JSON.stringify(template.content, null, 2)}

PERFIL DO NEGÓCIO:
- Tipo: ${businessData.businessType}
- Setor: ${businessData.industry}
- Tamanho: ${businessData.companySize || 'Não especificado'}
- Ferramentas Atuais: ${businessData.currentTools?.join(', ') || 'Não especificado'}
- Principais Desafios: ${businessData.painPoints?.join(', ') || 'Não especificado'}
- Objetivos: ${businessData.goals?.join(', ') || 'Não especificado'}

OPORTUNIDADES DE AUTOMAÇÃO IDENTIFICADAS:
${automationOpportunities.map(opp => `- ${opp}`).join('\n')}

INTEGRAÇÕES RECOMENDADAS:
${integrationSuggestions.map(int => `- ${int}`).join('\n')}

ANÁLISE GERADA (RESUMO):
${analysisResult.blueprint?.substring(0, 800)}...

INSTRUÇÕES DETALHADAS:
1. ESTRUTURA: Mantenha a estrutura JSON válida do N8N
2. NOMENCLATURA: Renomeie o workflow e nós para refletir o negócio específico
3. PERSONALIZAÇÃO: Ajuste parâmetros, mensagens e configurações para o contexto
4. IDs ÚNICOS: Gere novos UUIDs para todos os nós para evitar conflitos
5. RELEVÂNCIA: Foque em automações que resolvam os pontos de dor específicos
6. PRATICIDADE: Certifique-se de que o workflow seja implementável e útil

CUSTOMIZAÇÕES ESPECÍFICAS OBRIGATÓRIAS:
- Nome do workflow: "Automação [Tipo de Negócio] - [Principal Benefício]"
- Nós de IA: Customize prompts do sistema para o setor específico
- Integrações: Adicione/modifique integrações baseadas nas ferramentas mencionadas
- Triggers: Ajuste gatilhos para eventos relevantes ao negócio
- Outputs: Configure saídas para atender aos objetivos específicos

RETORNE APENAS O JSON CUSTOMIZADO, SEM EXPLICAÇÕES ADICIONAIS.`;
  }

  // Identify automation opportunities based on business data
  private identifyAutomationOpportunities(businessData: BusinessAnalysisData): string[] {
    const opportunities: string[] = [];

    // Based on business type
    const businessType = businessData.businessType?.toLowerCase() || '';
    if (businessType.includes('e-commerce') || businessType.includes('loja')) {
      opportunities.push('Automação de follow-up pós-venda');
      opportunities.push('Gestão automática de estoque');
      opportunities.push('Campanhas de email marketing segmentadas');
    } else if (businessType.includes('serviço') || businessType.includes('consultoria')) {
      opportunities.push('Qualificação automática de leads');
      opportunities.push('Agendamento inteligente de reuniões');
      opportunities.push('Follow-up automatizado de propostas');
    } else if (businessType.includes('saas') || businessType.includes('software')) {
      opportunities.push('Onboarding automatizado de usuários');
      opportunities.push('Análise de churn e retenção');
      opportunities.push('Suporte técnico automatizado');
    }

    // Based on pain points
    businessData.painPoints?.forEach(pain => {
      const painLower = pain.toLowerCase();
      if (painLower.includes('manual') || painLower.includes('repetitiv')) {
        opportunities.push('Automação de tarefas repetitivas');
      }
      if (painLower.includes('comunicação') || painLower.includes('contato')) {
        opportunities.push('Centralização de comunicação com clientes');
      }
      if (painLower.includes('dados') || painLower.includes('relatório')) {
        opportunities.push('Geração automática de relatórios');
      }
      if (painLower.includes('vendas') || painLower.includes('lead')) {
        opportunities.push('Pipeline de vendas automatizado');
      }
    });

    // Based on goals
    businessData.goals?.forEach(goal => {
      const goalLower = goal.toLowerCase();
      if (goalLower.includes('eficiência') || goalLower.includes('produtividade')) {
        opportunities.push('Otimização de processos internos');
      }
      if (goalLower.includes('crescimento') || goalLower.includes('escala')) {
        opportunities.push('Automação para escalabilidade');
      }
      if (goalLower.includes('cliente') || goalLower.includes('satisfação')) {
        opportunities.push('Melhoria da experiência do cliente');
      }
    });

    return opportunities.length > 0 ? opportunities : ['Automação de processos gerais', 'Integração de sistemas', 'Otimização de fluxos de trabalho'];
  }

  // Suggest integrations based on business data
  private suggestIntegrations(businessData: BusinessAnalysisData): string[] {
    const integrations: string[] = [];

    // Based on current tools
    businessData.currentTools?.forEach(tool => {
      const toolLower = tool.toLowerCase();
      if (toolLower.includes('gmail') || toolLower.includes('email')) {
        integrations.push('Gmail/Email - Automação de comunicação');
      }
      if (toolLower.includes('sheets') || toolLower.includes('excel')) {
        integrations.push('Google Sheets - Gestão de dados');
      }
      if (toolLower.includes('slack') || toolLower.includes('teams')) {
        integrations.push('Slack/Teams - Notificações internas');
      }
      if (toolLower.includes('crm') || toolLower.includes('salesforce')) {
        integrations.push('CRM - Gestão de relacionamento');
      }
      if (toolLower.includes('whatsapp') || toolLower.includes('telegram')) {
        integrations.push('WhatsApp/Telegram - Comunicação direta');
      }
    });

    // Based on industry
    const industry = businessData.industry?.toLowerCase() || '';
    if (industry.includes('e-commerce') || industry.includes('varejo')) {
      integrations.push('WooCommerce/Shopify - Gestão de loja');
      integrations.push('Stripe/PayPal - Processamento de pagamentos');
    } else if (industry.includes('marketing') || industry.includes('publicidade')) {
      integrations.push('Facebook Ads - Gestão de campanhas');
      integrations.push('Google Analytics - Análise de dados');
    } else if (industry.includes('educação') || industry.includes('ensino')) {
      integrations.push('Zoom/Meet - Automação de aulas');
      integrations.push('Moodle/Canvas - Gestão educacional');
    }

    // Default integrations
    if (integrations.length === 0) {
      integrations.push('Webhook - Integração personalizada');
      integrations.push('HTTP Request - APIs externas');
      integrations.push('Schedule Trigger - Automação temporal');
    }

    return integrations;
  }

  // Basic customization fallback
  private basicCustomization(workflow: any, businessData: BusinessAnalysisData): any {
    const customized = JSON.parse(JSON.stringify(workflow)); // Deep clone
    const nodeIdMapping: { [oldId: string]: string } = {};

    // Generate workflow name
    const workflowName = this.generateWorkflowName(businessData);
    if (customized.name) {
      customized.name = workflowName;
    }

    // Update nodes with new IDs and customizations
    if (customized.nodes) {
      customized.nodes.forEach((node: any) => {
        // Store old ID and generate new one
        const oldId = node.id;
        const newId = uuidv4();
        nodeIdMapping[oldId] = newId;
        node.id = newId;

        // Customize node name
        if (node.name) {
          node.name = this.customizeNodeName(node.name, businessData);
        }

        // Customize system messages in AI nodes
        if (node.parameters?.options?.systemMessage) {
          node.parameters.options.systemMessage = this.customizeSystemMessage(
            node.parameters.options.systemMessage,
            businessData
          );
        }

        // Customize prompts in AI nodes
        if (node.parameters?.prompt) {
          node.parameters.prompt = this.customizePrompt(
            node.parameters.prompt,
            businessData
          );
        }

        // Customize webhook URLs
        if (node.type === 'n8n-nodes-base.webhook' && node.parameters?.path) {
          node.parameters.path = this.customizeWebhookPath(
            node.parameters.path,
            businessData
          );
        }

        // Customize email templates
        if (node.type === 'n8n-nodes-base.emailSend' || node.type === 'n8n-nodes-base.gmail') {
          this.customizeEmailNode(node, businessData);
        }

        // Customize database/spreadsheet operations
        if (node.type?.includes('googleSheets') || node.type?.includes('airtable')) {
          this.customizeDataNode(node, businessData);
        }
      });
    }

    // Update connections with new IDs
    if (customized.connections) {
      const newConnections: any = {};
      Object.keys(customized.connections).forEach(oldNodeId => {
        const newNodeId = nodeIdMapping[oldNodeId] || oldNodeId;
        newConnections[newNodeId] = customized.connections[oldNodeId];

        // Update connection targets
        if (newConnections[newNodeId]) {
          Object.keys(newConnections[newNodeId]).forEach(outputIndex => {
            newConnections[newNodeId][outputIndex] = newConnections[newNodeId][outputIndex].map((connection: any) => ({
              ...connection,
              node: nodeIdMapping[connection.node] || connection.node
            }));
          });
        }
      });
      customized.connections = newConnections;
    }

    // Add metadata
    customized.meta = {
      ...customized.meta,
      generatedBy: 'Infra AI Analyst',
      generatedAt: new Date().toISOString(),
      businessType: businessData.businessType,
      industry: businessData.industry,
      version: '1.0'
    };

    return customized;
  }

  // Generate workflow name based on business data
  private generateWorkflowName(businessData: BusinessAnalysisData): string {
    const businessType = businessData.businessType || 'Empresarial';
    const industry = businessData.industry || 'Geral';

    // Identify main automation focus
    let focus = 'Automação';
    if (businessData.painPoints?.some(p => p.toLowerCase().includes('lead'))) {
      focus = 'Gestão de Leads';
    } else if (businessData.painPoints?.some(p => p.toLowerCase().includes('cliente'))) {
      focus = 'Atendimento ao Cliente';
    } else if (businessData.painPoints?.some(p => p.toLowerCase().includes('vendas'))) {
      focus = 'Pipeline de Vendas';
    } else if (businessData.painPoints?.some(p => p.toLowerCase().includes('marketing'))) {
      focus = 'Marketing Automation';
    }

    return `${focus} - ${businessType} (${industry})`;
  }

  // Customize node names
  private customizeNodeName(originalName: string, businessData: BusinessAnalysisData): string {
    const businessType = businessData.businessType || 'empresa';

    // Replace generic terms with business-specific ones
    return originalName
      .replace(/empresa|negócio|organização/gi, businessType)
      .replace(/cliente|usuário/gi, this.getCustomerTerm(businessData))
      .replace(/produto|serviço/gi, this.getOfferingTerm(businessData));
  }

  // Get appropriate customer term based on business
  private getCustomerTerm(businessData: BusinessAnalysisData): string {
    const businessType = businessData.businessType?.toLowerCase() || '';
    const industry = businessData.industry?.toLowerCase() || '';

    if (businessType.includes('saas') || businessType.includes('software')) {
      return 'usuário';
    } else if (industry.includes('educação') || industry.includes('ensino')) {
      return 'aluno';
    } else if (industry.includes('saúde') || industry.includes('médico')) {
      return 'paciente';
    } else if (businessType.includes('b2b')) {
      return 'empresa cliente';
    }

    return 'cliente';
  }

  // Get appropriate offering term based on business
  private getOfferingTerm(businessData: BusinessAnalysisData): string {
    const businessType = businessData.businessType?.toLowerCase() || '';
    const industry = businessData.industry?.toLowerCase() || '';

    if (businessType.includes('saas') || businessType.includes('software')) {
      return 'plataforma';
    } else if (industry.includes('educação') || industry.includes('ensino')) {
      return 'curso';
    } else if (industry.includes('consultoria') || industry.includes('serviço')) {
      return 'serviço';
    } else if (businessType.includes('e-commerce') || businessType.includes('loja')) {
      return 'produto';
    }

    return 'solução';
  }

  // Customize system message for AI nodes
  private customizeSystemMessage(originalMessage: string, businessData: BusinessAnalysisData): string {
    let customMessage = originalMessage;

    // Replace generic terms with business-specific ones
    if (businessData.businessType) {
      customMessage = customMessage.replace(
        /negócio|empresa|organização/gi,
        businessData.businessType
      );
    }

    // Add business context
    const contextAdditions: string[] = [];

    if (businessData.industry) {
      contextAdditions.push(`Especialização: Você tem expertise específica no setor de ${businessData.industry}.`);
    }

    if (businessData.companySize) {
      contextAdditions.push(`Contexto: Esta é uma ${businessData.companySize} empresa.`);
    }

    if (businessData.painPoints && businessData.painPoints.length > 0) {
      contextAdditions.push(`Pontos de atenção principais: ${businessData.painPoints.slice(0, 3).join(', ')}.`);
    }

    if (businessData.goals && businessData.goals.length > 0) {
      contextAdditions.push(`Objetivos do cliente: ${businessData.goals.slice(0, 3).join(', ')}.`);
    }

    if (businessData.currentTools && businessData.currentTools.length > 0) {
      contextAdditions.push(`Ferramentas já utilizadas: ${businessData.currentTools.slice(0, 5).join(', ')}.`);
    }

    if (contextAdditions.length > 0) {
      customMessage += '\n\n' + contextAdditions.join('\n');
    }

    return customMessage;
  }

  // Customize prompts for AI nodes
  private customizePrompt(originalPrompt: string, businessData: BusinessAnalysisData): string {
    let customPrompt = originalPrompt;

    // Replace placeholders with business-specific information
    const replacements = {
      '[BUSINESS_TYPE]': businessData.businessType || 'empresa',
      '[INDUSTRY]': businessData.industry || 'setor empresarial',
      '[COMPANY_SIZE]': businessData.companySize || 'empresa',
      '[CUSTOMER_TERM]': this.getCustomerTerm(businessData),
      '[OFFERING_TERM]': this.getOfferingTerm(businessData)
    };

    Object.entries(replacements).forEach(([placeholder, value]) => {
      customPrompt = customPrompt.replace(new RegExp(placeholder, 'g'), value);
    });

    return customPrompt;
  }

  // Customize webhook paths
  private customizeWebhookPath(originalPath: string, businessData: BusinessAnalysisData): string {
    const businessSlug = businessData.businessType?.toLowerCase().replace(/\s+/g, '-') || 'business';
    const industrySlug = businessData.industry?.toLowerCase().replace(/\s+/g, '-') || 'general';

    // Generate a unique path based on business data
    return `/${businessSlug}-${industrySlug}-automation-${Date.now()}`;
  }

  // Customize email nodes
  private customizeEmailNode(node: any, businessData: BusinessAnalysisData): void {
    if (node.parameters) {
      // Customize subject line
      if (node.parameters.subject) {
        node.parameters.subject = node.parameters.subject
          .replace(/\[BUSINESS\]/g, businessData.businessType || 'Nossa Empresa')
          .replace(/\[INDUSTRY\]/g, businessData.industry || 'setor');
      }

      // Customize email body
      if (node.parameters.text || node.parameters.html) {
        const emailContent = node.parameters.text || node.parameters.html;
        const customizedContent = emailContent
          .replace(/\[BUSINESS_TYPE\]/g, businessData.businessType || 'empresa')
          .replace(/\[INDUSTRY\]/g, businessData.industry || 'setor')
          .replace(/\[CUSTOMER_TERM\]/g, this.getCustomerTerm(businessData));

        if (node.parameters.text) {
          node.parameters.text = customizedContent;
        }
        if (node.parameters.html) {
          node.parameters.html = customizedContent;
        }
      }

      // Add business signature
      const signature = this.generateEmailSignature(businessData);
      if (node.parameters.text) {
        node.parameters.text += `\n\n${signature}`;
      }
      if (node.parameters.html) {
        node.parameters.html += `<br><br>${signature.replace(/\n/g, '<br>')}`;
      }
    }
  }

  // Customize data nodes (sheets, databases)
  private customizeDataNode(node: any, businessData: BusinessAnalysisData): void {
    if (node.parameters) {
      // Customize sheet/table names
      if (node.parameters.sheetName) {
        node.parameters.sheetName = `${businessData.businessType || 'Business'}_Data`;
      }

      // Customize column mappings based on business type
      if (node.parameters.columns) {
        this.customizeDataColumns(node.parameters.columns, businessData);
      }
    }
  }

  // Generate email signature
  private generateEmailSignature(businessData: BusinessAnalysisData): string {
    const businessType = businessData.businessType || 'Nossa Empresa';
    const industry = businessData.industry || 'setor empresarial';

    return `Atenciosamente,
Equipe ${businessType}
Especialistas em ${industry}

Esta mensagem foi enviada automaticamente pelo nosso sistema de automação.`;
  }

  // Customize data columns
  private customizeDataColumns(columns: any, businessData: BusinessAnalysisData): void {
    // This would customize column mappings based on business type
    // For example, e-commerce might have different columns than SaaS
    const businessType = businessData.businessType?.toLowerCase() || '';

    if (businessType.includes('e-commerce')) {
      // Add e-commerce specific columns
      if (columns.value) {
        columns.value['Product'] = '={{ $json.product }}';
        columns.value['Order_Value'] = '={{ $json.order_value }}';
      }
    } else if (businessType.includes('saas')) {
      // Add SaaS specific columns
      if (columns.value) {
        columns.value['Plan'] = '={{ $json.plan }}';
        columns.value['MRR'] = '={{ $json.mrr }}';
      }
    }
  }

  // Validate and clean the generated workflow
  private validateAndCleanWorkflow(workflow: any): any {
    // Basic validation
    if (!workflow.nodes || !Array.isArray(workflow.nodes)) {
      throw new Error('Invalid workflow: missing or invalid nodes array');
    }

    if (!workflow.connections || typeof workflow.connections !== 'object') {
      throw new Error('Invalid workflow: missing or invalid connections object');
    }

    // Ensure all nodes have required fields
    workflow.nodes.forEach((node: any, index: number) => {
      if (!node.id) {
        node.id = uuidv4();
      }
      if (!node.name) {
        node.name = `Node ${index + 1}`;
      }
      if (!node.type) {
        throw new Error(`Node ${node.id} missing type`);
      }
    });

    // Add metadata
    workflow.meta = {
      ...workflow.meta,
      generatedBy: 'Infra AI Analyst',
      generatedAt: new Date().toISOString(),
      version: '1.0'
    };

    return workflow;
  }

  // Get available templates info
  getAvailableTemplates(): Array<{ name: string; description?: string }> {
    return this.templates.map(template => ({
      name: template.name,
      description: template.content.name || 'N8N Workflow Template'
    }));
  }
}

export const n8nJsonService = new N8nJsonService();
