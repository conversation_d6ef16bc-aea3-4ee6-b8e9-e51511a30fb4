import Stripe from 'stripe';
import { logger, logError } from '../utils/logger';

export interface CreatePaymentIntentData {
  amount: number; // in cents
  currency: string;
  customerEmail: string;
  customerName: string;
  description: string;
  metadata?: any;
}

export interface CreateCustomerData {
  email: string;
  name: string;
  phone?: string;
  address?: {
    line1: string;
    city: string;
    state: string;
    postal_code: string;
    country: string;
  };
}

class StripeService {
  private stripe: Stripe;

  constructor() {
    if (!process.env.STRIPE_SECRET_KEY) {
      throw new Error('STRIPE_SECRET_KEY environment variable is required');
    }

    this.stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {
      apiVersion: '2023-10-16',
      typescript: true
    });

    logger.info('Stripe service initialized');
  }

  // Create payment intent
  async createPaymentIntent(data: CreatePaymentIntentData): Promise<Stripe.PaymentIntent> {
    try {
      // Find or create customer
      let customer = await this.findCustomerByEmail(data.customerEmail);
      
      if (!customer) {
        customer = await this.createCustomer({
          email: data.customerEmail,
          name: data.customerName
        });
      }

      const paymentIntent = await this.stripe.paymentIntents.create({
        amount: data.amount,
        currency: data.currency,
        customer: customer.id,
        description: data.description,
        metadata: data.metadata || {},
        automatic_payment_methods: {
          enabled: true
        },
        receipt_email: data.customerEmail
      });

      logger.info('Payment intent created', {
        paymentIntentId: paymentIntent.id,
        amount: data.amount,
        currency: data.currency,
        customerEmail: data.customerEmail
      });

      return paymentIntent;
    } catch (error) {
      logError('Error creating payment intent', error);
      throw error;
    }
  }

  // Create customer
  async createCustomer(data: CreateCustomerData): Promise<Stripe.Customer> {
    try {
      const customer = await this.stripe.customers.create({
        email: data.email,
        name: data.name,
        phone: data.phone,
        address: data.address
      });

      logger.info('Stripe customer created', {
        customerId: customer.id,
        email: data.email
      });

      return customer;
    } catch (error) {
      logError('Error creating customer', error);
      throw error;
    }
  }

  // Find customer by email
  async findCustomerByEmail(email: string): Promise<Stripe.Customer | null> {
    try {
      const customers = await this.stripe.customers.list({
        email,
        limit: 1
      });

      return customers.data.length > 0 ? customers.data[0] : null;
    } catch (error) {
      logError('Error finding customer by email', error);
      return null;
    }
  }

  // Get payment intent
  async getPaymentIntent(paymentIntentId: string): Promise<Stripe.PaymentIntent> {
    try {
      const paymentIntent = await this.stripe.paymentIntents.retrieve(paymentIntentId);
      return paymentIntent;
    } catch (error) {
      logError('Error retrieving payment intent', error);
      throw error;
    }
  }

  // Update payment intent
  async updatePaymentIntent(
    paymentIntentId: string, 
    updates: Partial<Stripe.PaymentIntentUpdateParams>
  ): Promise<Stripe.PaymentIntent> {
    try {
      const paymentIntent = await this.stripe.paymentIntents.update(paymentIntentId, updates);
      
      logger.info('Payment intent updated', {
        paymentIntentId,
        updates: Object.keys(updates)
      });

      return paymentIntent;
    } catch (error) {
      logError('Error updating payment intent', error);
      throw error;
    }
  }

  // Cancel payment intent
  async cancelPaymentIntent(paymentIntentId: string): Promise<Stripe.PaymentIntent> {
    try {
      const paymentIntent = await this.stripe.paymentIntents.cancel(paymentIntentId);
      
      logger.info('Payment intent cancelled', { paymentIntentId });

      return paymentIntent;
    } catch (error) {
      logError('Error cancelling payment intent', error);
      throw error;
    }
  }

  // Create refund
  async createRefund(
    chargeId: string, 
    amount?: number, 
    reason?: string
  ): Promise<Stripe.Refund> {
    try {
      const refund = await this.stripe.refunds.create({
        charge: chargeId,
        amount,
        reason: reason as Stripe.RefundCreateParams.Reason
      });

      logger.info('Refund created', {
        refundId: refund.id,
        chargeId,
        amount: refund.amount
      });

      return refund;
    } catch (error) {
      logError('Error creating refund', error);
      throw error;
    }
  }

  // Get customer payments
  async getCustomerPayments(customerId: string, limit: number = 10): Promise<Stripe.PaymentIntent[]> {
    try {
      const paymentIntents = await this.stripe.paymentIntents.list({
        customer: customerId,
        limit
      });

      return paymentIntents.data;
    } catch (error) {
      logError('Error getting customer payments', error);
      throw error;
    }
  }

  // Create subscription (for future use)
  async createSubscription(
    customerId: string,
    priceId: string,
    metadata?: any
  ): Promise<Stripe.Subscription> {
    try {
      const subscription = await this.stripe.subscriptions.create({
        customer: customerId,
        items: [{ price: priceId }],
        metadata: metadata || {},
        payment_behavior: 'default_incomplete',
        payment_settings: { save_default_payment_method: 'on_subscription' },
        expand: ['latest_invoice.payment_intent']
      });

      logger.info('Subscription created', {
        subscriptionId: subscription.id,
        customerId,
        priceId
      });

      return subscription;
    } catch (error) {
      logError('Error creating subscription', error);
      throw error;
    }
  }

  // Construct webhook event
  constructWebhookEvent(payload: any, signature: string): Stripe.Event {
    try {
      const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;
      
      if (!webhookSecret) {
        throw new Error('STRIPE_WEBHOOK_SECRET environment variable is required');
      }

      const event = this.stripe.webhooks.constructEvent(
        payload,
        signature,
        webhookSecret
      );

      return event;
    } catch (error) {
      logError('Error constructing webhook event', error);
      throw error;
    }
  }

  // Get payment methods for customer
  async getCustomerPaymentMethods(
    customerId: string,
    type: string = 'card'
  ): Promise<Stripe.PaymentMethod[]> {
    try {
      const paymentMethods = await this.stripe.paymentMethods.list({
        customer: customerId,
        type: type as Stripe.PaymentMethodListParams.Type
      });

      return paymentMethods.data;
    } catch (error) {
      logError('Error getting customer payment methods', error);
      throw error;
    }
  }

  // Create setup intent (for saving payment methods)
  async createSetupIntent(customerId: string): Promise<Stripe.SetupIntent> {
    try {
      const setupIntent = await this.stripe.setupIntents.create({
        customer: customerId,
        payment_method_types: ['card'],
        usage: 'off_session'
      });

      logger.info('Setup intent created', {
        setupIntentId: setupIntent.id,
        customerId
      });

      return setupIntent;
    } catch (error) {
      logError('Error creating setup intent', error);
      throw error;
    }
  }

  // Get balance
  async getBalance(): Promise<Stripe.Balance> {
    try {
      const balance = await this.stripe.balance.retrieve();
      return balance;
    } catch (error) {
      logError('Error getting balance', error);
      throw error;
    }
  }

  // Get recent charges
  async getRecentCharges(limit: number = 10): Promise<Stripe.Charge[]> {
    try {
      const charges = await this.stripe.charges.list({
        limit,
        expand: ['data.customer']
      });

      return charges.data;
    } catch (error) {
      logError('Error getting recent charges', error);
      throw error;
    }
  }

  // Check if service is available
  isAvailable(): boolean {
    return !!process.env.STRIPE_SECRET_KEY;
  }

  // Get publishable key
  getPublishableKey(): string | undefined {
    return process.env.STRIPE_PUBLISHABLE_KEY;
  }
}

export const stripeService = new StripeService();
